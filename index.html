<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة النقابات - Système de Gestion Syndic</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/arabic.css" id="arabic-styles">
    <link rel="stylesheet" href="css/french.css" id="french-styles" disabled>
</head>
<body>
    <!-- Language Toggle -->
    <div class="language-toggle">
        <button type="button" id="lang-ar" class="lang-btn active" data-lang="ar">
            <i class="bi bi-translate"></i> العربية
        </button>
        <button type="button" id="lang-fr" class="lang-btn" data-lang="fr">
            <i class="bi bi-translate"></i> Français
        </button>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1 class="logo" data-key="app_title">نظام إدارة النقابات</h1>
            <div class="header-actions">
                <button type="button" class="btn-icon" id="theme-toggle">
                    <i class="bi bi-moon"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="nav-header">
            <h3 data-key="navigation">التنقل</h3>
            <button type="button" class="btn-icon" id="sidebar-toggle">
                <i class="bi bi-list"></i>
            </button>
        </div>
        
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="#dashboard" class="nav-link active" data-section="dashboard">
                    <i class="bi bi-house"></i>
                    <span data-key="dashboard">لوحة التحكم</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#members" class="nav-link" data-section="members">
                    <i class="bi bi-people"></i>
                    <span data-key="members">إدارة الأعضاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#subscriptions" class="nav-link" data-section="subscriptions">
                    <i class="bi bi-calendar-check"></i>
                    <span data-key="subscriptions">الاشتراكات</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#receipts" class="nav-link" data-section="receipts">
                    <i class="bi bi-receipt"></i>
                    <span data-key="receipts">الإيصالات</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#reports" class="nav-link" data-section="reports">
                    <i class="bi bi-graph-up"></i>
                    <span data-key="reports">التقارير</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#court-docs" class="nav-link" data-section="court-docs">
                    <i class="bi bi-file-earmark-text"></i>
                    <span data-key="court_docs">الوثائق القانونية</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#settings" class="nav-link" data-section="settings">
                    <i class="bi bi-gear"></i>
                    <span data-key="settings">الإعدادات</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <main class="main-content" id="main-content">
        <!-- Dashboard Section -->
        <section id="dashboard-section" class="content-section active">
            <div class="container">
                <div class="page-header">
                    <h2 data-key="dashboard">لوحة التحكم</h2>
                    <p data-key="dashboard_subtitle">نظرة عامة على النظام</p>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-members">0</h3>
                            <p data-key="total_members">إجمالي الأعضاء</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="bi bi-cash-coin"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-dues">0 د.ج</h3>
                            <p data-key="total_dues">إجمالي المستحقات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="overdue-count">0</h3>
                            <p data-key="overdue_payments">المدفوعات المتأخرة</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="bi bi-receipt"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="receipts-count">0</h3>
                            <p data-key="receipts_issued">الإيصالات المصدرة</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="card">
                    <div class="card-header">
                        <h3 data-key="recent_activities">الأنشطة الأخيرة</h3>
                    </div>
                    <div class="card-body">
                        <div id="recent-activities" class="activities-list">
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="bi bi-info-circle"></i>
                                </div>
                                <div class="activity-content">
                                    <p data-key="no_activities">لا توجد أنشطة حديثة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Members Section -->
        <section id="members-section" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h2 data-key="members">إدارة الأعضاء</h2>
                    <button type="button" class="btn btn-primary" id="add-member-btn">
                        <i class="bi bi-plus"></i>
                        <span data-key="add_member">إضافة عضو</span>
                    </button>
                </div>

                <!-- Search and Filters -->
                <div class="filters-section">
                    <div class="search-box">
                        <i class="bi bi-search"></i>
                        <input type="text" id="member-search" placeholder="البحث عن عضو..." data-key-placeholder="search_member">
                    </div>
                    <div class="filter-buttons">
                        <button class="btn btn-outline active" id="filter-all" data-filter="all">
                            <span data-key="all">الكل</span>
                        </button>
                        <button class="btn btn-outline" id="filter-active" data-filter="active">
                            <span data-key="active">نشط</span>
                        </button>
                        <button class="btn btn-outline" id="filter-overdue" data-filter="overdue">
                            <span data-key="overdue">متأخر</span>
                        </button>
                    </div>
                </div>

                <!-- Members Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table" id="members-table">
                                <thead>
                                    <tr>
                                        <th data-key="full_name">الاسم الكامل</th>
                                        <th data-key="group_number">رقم المجموعة</th>
                                        <th data-key="building_apt">البناء/الشقة</th>
                                        <th data-key="phone">الهاتف</th>
                                        <th data-key="status">الحالة</th>
                                        <th data-key="actions">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="members-tbody">
                                    <tr>
                                        <td colspan="6" class="text-center">
                                            <span data-key="no_members">لا توجد أعضاء مسجلين</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Subscriptions Section -->
        <section id="subscriptions-section" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h2 data-key="subscriptions">الاشتراكات</h2>
                    <button class="btn btn-primary" id="add-subscription-btn">
                        <i class="bi bi-plus"></i>
                        <span data-key="add_subscription">إضافة اشتراك</span>
                    </button>
                </div>

                <!-- Subscriptions Grid -->
                <div class="subscriptions-grid" id="subscriptions-grid">
                    <!-- Subscriptions will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Receipts Section -->
        <section id="receipts-section" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h2 data-key="receipts">الإيصالات</h2>
                    <button class="btn btn-primary" id="create-receipt-btn">
                        <i class="bi bi-plus"></i>
                        <span data-key="create_receipt">إنشاء إيصال</span>
                    </button>
                </div>

                <!-- Receipts Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table" id="receipts-table">
                                <thead>
                                    <tr>
                                        <th data-key="receipt_number">رقم الإيصال</th>
                                        <th data-key="full_name">اسم العضو</th>
                                        <th data-key="amount">المبلغ</th>
                                        <th data-key="receipt_date">التاريخ</th>
                                        <th data-key="payment_method">طريقة الدفع</th>
                                        <th data-key="actions">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="receipts-tbody">
                                    <tr>
                                        <td colspan="6" class="text-center">
                                            <span data-key="no_receipts">لا توجد إيصالات</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reports-section" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h2 data-key="reports">التقارير</h2>
                </div>

                <!-- Report Types -->
                <div class="reports-grid">
                    <div class="report-card">
                        <div class="report-icon">
                            <i class="bi bi-calendar-day"></i>
                        </div>
                        <div class="report-content">
                            <h3 data-key="daily_report">التقرير اليومي</h3>
                            <p>تقرير المدفوعات والأنشطة اليومية</p>
                            <button class="btn btn-outline" onclick="generateReport('daily')">
                                <span data-key="generate_report">إنشاء التقرير</span>
                            </button>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-icon">
                            <i class="bi bi-calendar-month"></i>
                        </div>
                        <div class="report-content">
                            <h3 data-key="monthly_report">التقرير الشهري</h3>
                            <p>تقرير شامل للشهر الحالي</p>
                            <button class="btn btn-outline" onclick="generateReport('monthly')">
                                <span data-key="generate_report">إنشاء التقرير</span>
                            </button>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-icon">
                            <i class="bi bi-calendar-year"></i>
                        </div>
                        <div class="report-content">
                            <h3 data-key="annual_report">التقرير السنوي</h3>
                            <p>تقرير شامل للسنة الحالية</p>
                            <button class="btn btn-outline" onclick="generateReport('annual')">
                                <span data-key="generate_report">إنشاء التقرير</span>
                            </button>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-icon">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="report-content">
                            <h3 data-key="overdue_report">تقرير المتأخرات</h3>
                            <p>قائمة بالأعضاء المتأخرين في الدفع</p>
                            <button class="btn btn-outline" onclick="generateReport('overdue')">
                                <span data-key="generate_report">إنشاء التقرير</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Court Documents Section -->
        <section id="court-docs-section" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h2 data-key="court_docs">الوثائق القانونية</h2>
                    <button class="btn btn-primary" id="add-court-doc-btn">
                        <i class="bi bi-plus"></i>
                        <span data-key="add_court_doc">إضافة وثيقة</span>
                    </button>
                </div>

                <!-- Document Types -->
                <div class="court-docs-grid">
                    <div class="doc-type-card" onclick="createCourtDoc('notice_of_dues')">
                        <div class="doc-icon">
                            <i class="bi bi-bell"></i>
                        </div>
                        <h3 data-key="notice_of_dues">إشعار الاستحقاق</h3>
                    </div>

                    <div class="doc-type-card" onclick="createCourtDoc('ownership_certificate')">
                        <div class="doc-icon">
                            <i class="bi bi-award"></i>
                        </div>
                        <h3 data-key="ownership_certificate">شهادة الملكية</h3>
                    </div>

                    <div class="doc-type-card" onclick="createCourtDoc('court_order_request')">
                        <div class="doc-icon">
                            <i class="bi bi-file-earmark-text"></i>
                        </div>
                        <h3 data-key="court_order_request">طلب أمر قضائي</h3>
                    </div>

                    <div class="doc-type-card" onclick="createCourtDoc('precautionary_seizure')">
                        <div class="doc-icon">
                            <i class="bi bi-shield-exclamation"></i>
                        </div>
                        <h3 data-key="precautionary_seizure">أمر الحجز الاحتياطي</h3>
                    </div>

                    <div class="doc-type-card" onclick="createCourtDoc('legal_status')">
                        <div class="doc-icon">
                            <i class="bi bi-person-badge"></i>
                        </div>
                        <h3 data-key="legal_status">الوضعية القانونية</h3>
                    </div>

                    <div class="doc-type-card" onclick="createCourtDoc('judgment_registration')">
                        <div class="doc-icon">
                            <i class="bi bi-journal-bookmark"></i>
                        </div>
                        <h3 data-key="judgment_registration">تسجيل الأحكام</h3>
                    </div>
                </div>
            </div>
        </section>

        <!-- Settings Section -->
        <section id="settings-section" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h2 data-key="settings">الإعدادات</h2>
                </div>

                <div class="settings-grid">
                    <!-- Organization Settings -->
                    <div class="card">
                        <div class="card-header">
                            <h3 data-key="organization_info">معلومات المؤسسة</h3>
                        </div>
                        <div class="card-body">
                            <form id="organization-form">
                                <div class="form-group">
                                    <label data-key="organization_name">اسم المؤسسة</label>
                                    <input type="text" id="org-name" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label data-key="organization_address">عنوان المؤسسة</label>
                                    <textarea id="org-address" class="form-control" rows="3"></textarea>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label data-key="organization_phone">هاتف المؤسسة</label>
                                        <input type="tel" id="org-phone" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label data-key="organization_email">بريد المؤسسة</label>
                                        <input type="email" id="org-email" class="form-control">
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" onclick="saveOrganizationSettings()">
                                    <span data-key="save">حفظ</span>
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Data Management -->
                    <div class="card">
                        <div class="card-header">
                            <h3>إدارة البيانات</h3>
                        </div>
                        <div class="card-body">
                            <div class="data-actions">
                                <button class="btn btn-outline" onclick="exportData()">
                                    <i class="bi bi-download"></i>
                                    تصدير البيانات
                                </button>
                                <button class="btn btn-outline" onclick="importData()">
                                    <i class="bi bi-upload"></i>
                                    استيراد البيانات
                                </button>
                                <button class="btn btn-danger" onclick="clearAllData()">
                                    <i class="bi bi-trash"></i>
                                    مسح جميع البيانات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <i class="bi bi-arrow-clockwise"></i>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/storage.js"></script>
    <script src="js/language.js"></script>
    <script src="js/members.js"></script>
    <script src="js/subscriptions.js"></script>
    <script src="js/receipts.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
