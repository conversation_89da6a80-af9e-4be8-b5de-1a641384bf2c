/**
 * Reports and Court Documents Management System
 */

class ReportsManager {
    constructor() {
        this.initializeReports();
    }

    /**
     * Initialize reports management
     */
    initializeReports() {
        // Reports are generated on demand, no initialization needed
    }

    /**
     * Generate report based on type
     */
    generateReport(type) {
        switch (type) {
            case 'daily':
                this.generateDailyReport();
                break;
            case 'monthly':
                this.generateMonthlyReport();
                break;
            case 'annual':
                this.generateAnnualReport();
                break;
            case 'overdue':
                this.generateOverdueReport();
                break;
            default:
                console.error('Unknown report type:', type);
        }
    }

    /**
     * Generate daily report
     */
    generateDailyReport() {
        const today = new Date().toISOString().split('T')[0];
        const receipts = window.storage.getReceipts().filter(receipt => 
            receipt.receiptDate === today
        );

        const totalAmount = receipts.reduce((sum, receipt) => sum + receipt.amount, 0);
        const receiptCount = receipts.length;

        const reportContent = this.createReportHTML({
            title: 'التقرير اليومي',
            date: window.languageManager.formatDate(today),
            summary: [
                { label: 'عدد الإيصالات', value: receiptCount },
                { label: 'إجمالي المبلغ', value: window.languageManager.formatCurrency(totalAmount) }
            ],
            details: receipts.map(receipt => {
                const member = window.storage.getMemberById(receipt.memberId);
                return {
                    receiptNumber: receipt.receiptNumber,
                    memberName: member ? member.fullName : 'غير معروف',
                    amount: window.languageManager.formatCurrency(receipt.amount),
                    paymentMethod: this.getPaymentMethodText(receipt.paymentMethod)
                };
            })
        });

        this.showReportModal(reportContent);
    }

    /**
     * Generate monthly report
     */
    generateMonthlyReport() {
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth();
        const currentYear = currentDate.getFullYear();

        const receipts = window.storage.getReceipts().filter(receipt => {
            const receiptDate = new Date(receipt.receiptDate);
            return receiptDate.getMonth() === currentMonth && receiptDate.getFullYear() === currentYear;
        });

        const totalAmount = receipts.reduce((sum, receipt) => sum + receipt.amount, 0);
        const receiptCount = receipts.length;

        // Group by payment method
        const paymentMethods = receipts.reduce((acc, receipt) => {
            acc[receipt.paymentMethod] = (acc[receipt.paymentMethod] || 0) + receipt.amount;
            return acc;
        }, {});

        const monthName = currentDate.toLocaleDateString('ar-DZ', { month: 'long', year: 'numeric' });

        const reportContent = this.createReportHTML({
            title: 'التقرير الشهري',
            date: monthName,
            summary: [
                { label: 'عدد الإيصالات', value: receiptCount },
                { label: 'إجمالي المبلغ', value: window.languageManager.formatCurrency(totalAmount) },
                ...Object.entries(paymentMethods).map(([method, amount]) => ({
                    label: this.getPaymentMethodText(method),
                    value: window.languageManager.formatCurrency(amount)
                }))
            ],
            details: receipts.map(receipt => {
                const member = window.storage.getMemberById(receipt.memberId);
                return {
                    receiptNumber: receipt.receiptNumber,
                    memberName: member ? member.fullName : 'غير معروف',
                    amount: window.languageManager.formatCurrency(receipt.amount),
                    date: window.languageManager.formatDate(receipt.receiptDate),
                    paymentMethod: this.getPaymentMethodText(receipt.paymentMethod)
                };
            })
        });

        this.showReportModal(reportContent);
    }

    /**
     * Generate annual report
     */
    generateAnnualReport() {
        const currentYear = new Date().getFullYear();
        const receipts = window.storage.getReceipts().filter(receipt => {
            const receiptDate = new Date(receipt.receiptDate);
            return receiptDate.getFullYear() === currentYear;
        });

        const totalAmount = receipts.reduce((sum, receipt) => sum + receipt.amount, 0);
        const receiptCount = receipts.length;

        // Group by month
        const monthlyData = receipts.reduce((acc, receipt) => {
            const month = new Date(receipt.receiptDate).getMonth();
            acc[month] = (acc[month] || 0) + receipt.amount;
            return acc;
        }, {});

        const reportContent = this.createReportHTML({
            title: 'التقرير السنوي',
            date: currentYear.toString(),
            summary: [
                { label: 'عدد الإيصالات', value: receiptCount },
                { label: 'إجمالي المبلغ', value: window.languageManager.formatCurrency(totalAmount) },
                { label: 'متوسط شهري', value: window.languageManager.formatCurrency(totalAmount / 12) }
            ],
            details: Object.entries(monthlyData).map(([month, amount]) => ({
                month: new Date(currentYear, month).toLocaleDateString('ar-DZ', { month: 'long' }),
                amount: window.languageManager.formatCurrency(amount)
            }))
        });

        this.showReportModal(reportContent);
    }

    /**
     * Generate overdue report
     */
    generateOverdueReport() {
        const members = window.storage.getMembers();
        const currentYear = new Date().getFullYear();
        const receipts = window.storage.getReceipts();

        const overdueMembers = members.filter(member => {
            // Check if member has paid for current year
            const memberReceipts = receipts.filter(receipt => 
                receipt.memberId === member.id &&
                new Date(receipt.receiptDate).getFullYear() === currentYear
            );
            
            return memberReceipts.length === 0;
        });

        const reportContent = this.createReportHTML({
            title: 'تقرير المتأخرات',
            date: `السنة ${currentYear}`,
            summary: [
                { label: 'عدد الأعضاء المتأخرين', value: overdueMembers.length },
                { label: 'إجمالي الأعضاء', value: members.length },
                { label: 'نسبة المتأخرين', value: `${((overdueMembers.length / members.length) * 100).toFixed(1)}%` }
            ],
            details: overdueMembers.map(member => ({
                memberName: member.fullName,
                groupNumber: member.groupNumber,
                buildingApt: member.buildingApt,
                phone: member.phone || 'غير محدد'
            }))
        });

        this.showReportModal(reportContent);
    }

    /**
     * Create report HTML
     */
    createReportHTML(data) {
        const settings = window.storage.getSettings();
        
        return `
            <div class="report-content">
                <div class="report-header">
                    <h1>${settings.organizationName || 'نقابة الملاك'}</h1>
                    <h2>${data.title}</h2>
                    <p>التاريخ: ${data.date}</p>
                    <p>تاريخ الإنشاء: ${window.languageManager.formatDate(new Date().toISOString())}</p>
                </div>
                
                <div class="report-summary">
                    <h3>الملخص</h3>
                    <table class="summary-table">
                        ${data.summary.map(item => `
                            <tr>
                                <td>${item.label}</td>
                                <td>${item.value}</td>
                            </tr>
                        `).join('')}
                    </table>
                </div>
                
                ${data.details && data.details.length > 0 ? `
                    <div class="report-details">
                        <h3>التفاصيل</h3>
                        <table class="details-table">
                            <thead>
                                <tr>
                                    ${Object.keys(data.details[0]).map(key => `<th>${this.getColumnHeader(key)}</th>`).join('')}
                                </tr>
                            </thead>
                            <tbody>
                                ${data.details.map(row => `
                                    <tr>
                                        ${Object.values(row).map(value => `<td>${value}</td>`).join('')}
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * Get column header text
     */
    getColumnHeader(key) {
        const headers = {
            receiptNumber: 'رقم الإيصال',
            memberName: 'اسم العضو',
            amount: 'المبلغ',
            date: 'التاريخ',
            paymentMethod: 'طريقة الدفع',
            month: 'الشهر',
            groupNumber: 'رقم المجموعة',
            buildingApt: 'البناء/الشقة',
            phone: 'الهاتف'
        };
        return headers[key] || key;
    }

    /**
     * Show report modal
     */
    showReportModal(content) {
        const modalHtml = `
            <div class="modal-overlay" id="report-modal">
                <div class="modal large-modal">
                    <div class="modal-header">
                        <h3>تقرير</h3>
                        <div class="modal-actions">
                            <button type="button" class="btn btn-outline" onclick="reportsManager.printReport()">
                                <i class="bi bi-printer"></i>
                                طباعة
                            </button>
                            <button type="button" class="btn-icon" onclick="reportsManager.closeReportModal()">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        document.body.classList.add('modal-open');
    }

    /**
     * Print report
     */
    printReport() {
        const reportContent = document.querySelector('#report-modal .report-content');
        if (!reportContent) return;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>تقرير</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; text-align: right; }
                        .report-header { text-align: center; margin-bottom: 30px; }
                        .summary-table, .details-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        .summary-table th, .summary-table td,
                        .details-table th, .details-table td { 
                            border: 1px solid #ddd; padding: 8px; text-align: right; 
                        }
                        .summary-table th, .details-table th { background-color: #f5f5f5; }
                        h1, h2, h3 { color: #333; }
                    </style>
                </head>
                <body>
                    ${reportContent.outerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    /**
     * Close report modal
     */
    closeReportModal() {
        const modal = document.getElementById('report-modal');
        if (modal) {
            modal.remove();
            document.body.classList.remove('modal-open');
        }
    }

    /**
     * Get payment method text
     */
    getPaymentMethodText(method) {
        const methods = {
            cash: 'نقداً',
            check: 'شيك',
            bank_transfer: 'تحويل بنكي'
        };
        return methods[method] || method;
    }
}

/**
 * Court Documents Manager
 */
class CourtDocsManager {
    constructor() {
        this.initializeCourtDocs();
    }

    /**
     * Initialize court documents management
     */
    initializeCourtDocs() {
        // Court documents are created on demand
    }

    /**
     * Create court document
     */
    createCourtDoc(type) {
        const members = window.storage.getMembers();
        
        if (members.length === 0) {
            alert('يجب إضافة أعضاء أولاً قبل إنشاء الوثائق القانونية');
            return;
        }

        // For now, just show a simple selection modal
        // In a full implementation, you'd create specific forms for each document type
        this.showMemberSelectionModal(type);
    }

    /**
     * Show member selection modal for court documents
     */
    showMemberSelectionModal(docType) {
        const members = window.storage.getMembers();
        const docTitle = this.getDocumentTitle(docType);

        const modalHtml = `
            <div class="modal-overlay" id="court-doc-modal">
                <div class="modal">
                    <div class="modal-header">
                        <h3>${docTitle}</h3>
                        <button type="button" class="btn-icon" onclick="courtDocsManager.closeCourtDocModal()">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>اختر العضو</label>
                            <select id="courtDocMember" class="form-control" required>
                                <option value="">اختر العضو</option>
                                ${members.map(member => `
                                    <option value="${member.id}">${member.fullName} - ${member.groupNumber}</option>
                                `).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label>ملاحظات إضافية</label>
                            <textarea id="courtDocNotes" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="courtDocsManager.closeCourtDocModal()">
                            إلغاء
                        </button>
                        <button type="button" class="btn btn-primary" onclick="courtDocsManager.generateCourtDoc('${docType}')">
                            إنشاء الوثيقة
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        document.body.classList.add('modal-open');
    }

    /**
     * Generate court document
     */
    generateCourtDoc(docType) {
        const memberId = document.getElementById('courtDocMember').value;
        const notes = document.getElementById('courtDocNotes').value.trim();

        if (!memberId) {
            alert('يجب اختيار عضو');
            return;
        }

        const member = window.storage.getMemberById(memberId);
        const settings = window.storage.getSettings();

        // Create document content based on type
        const docContent = this.createDocumentContent(docType, member, settings, notes);
        
        // Save document
        const courtDoc = {
            type: docType,
            title: this.getDocumentTitle(docType),
            memberId: memberId,
            content: docContent,
            notes: notes
        };

        window.storage.saveCourtDoc(courtDoc);
        
        // Show document for printing
        this.showDocumentModal(docContent);
        this.closeCourtDocModal();
    }

    /**
     * Create document content
     */
    createDocumentContent(type, member, settings, notes) {
        const currentDate = new Date().toLocaleDateString('ar-DZ');
        
        switch (type) {
            case 'notice_of_dues':
                return this.createNoticeOfDues(member, settings, currentDate, notes);
            case 'ownership_certificate':
                return this.createOwnershipCertificate(member, settings, currentDate, notes);
            default:
                return this.createGenericDocument(type, member, settings, currentDate, notes);
        }
    }

    /**
     * Create notice of dues document
     */
    createNoticeOfDues(member, settings, date, notes) {
        return `
            <div class="document-content">
                <div class="document-header">
                    <h1>${settings.organizationName || 'نقابة الملاك'}</h1>
                    <p>${settings.organizationAddress || ''}</p>
                    <p>هاتف: ${settings.organizationPhone || ''}</p>
                </div>
                
                <div class="document-body">
                    <h2 style="text-align: center;">إشعار استحقاق</h2>
                    
                    <p>التاريخ: ${date}</p>
                    
                    <p>إلى السيد/السيدة: <strong>${member.fullName}</strong></p>
                    <p>رقم المجموعة: <strong>${member.groupNumber}</strong></p>
                    <p>البناء/الشقة: <strong>${member.buildingApt}</strong></p>
                    
                    <p>تحية طيبة وبعد،</p>
                    
                    <p>نحيطكم علماً بأن عليكم مستحقات مالية لصالح النقابة، ونرجو منكم التكرم بتسديدها في أقرب وقت ممكن.</p>
                    
                    <p>للاستفسار يرجى الاتصال على الرقم: ${settings.organizationPhone || ''}</p>
                    
                    ${notes ? `<p><strong>ملاحظات:</strong> ${notes}</p>` : ''}
                    
                    <div style="margin-top: 50px;">
                        <p>رئيس النقابة</p>
                        <p>التوقيع: ________________</p>
                        <p>الختم: ________________</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create ownership certificate
     */
    createOwnershipCertificate(member, settings, date, notes) {
        return `
            <div class="document-content">
                <div class="document-header">
                    <h1>${settings.organizationName || 'نقابة الملاك'}</h1>
                    <p>${settings.organizationAddress || ''}</p>
                </div>
                
                <div class="document-body">
                    <h2 style="text-align: center;">شهادة ملكية</h2>
                    
                    <p>التاريخ: ${date}</p>
                    
                    <p>نشهد نحن ${settings.organizationName || 'نقابة الملاك'} بأن:</p>
                    
                    <p>السيد/السيدة: <strong>${member.fullName}</strong></p>
                    <p>هو/هي مالك/مالكة للعقار رقم: <strong>${member.groupNumber}</strong></p>
                    <p>الواقع في: <strong>${member.buildingApt}</strong></p>
                    <p>السند العقاري: <strong>${member.landTitle || 'غير محدد'}</strong></p>
                    
                    <p>وقد أعطيت له/لها هذه الشهادة للاستعمال عند الحاجة.</p>
                    
                    ${notes ? `<p><strong>ملاحظات:</strong> ${notes}</p>` : ''}
                    
                    <div style="margin-top: 50px;">
                        <p>رئيس النقابة</p>
                        <p>التوقيع: ________________</p>
                        <p>الختم: ________________</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create generic document
     */
    createGenericDocument(type, member, settings, date, notes) {
        return `
            <div class="document-content">
                <div class="document-header">
                    <h1>${settings.organizationName || 'نقابة الملاك'}</h1>
                    <p>${settings.organizationAddress || ''}</p>
                </div>
                
                <div class="document-body">
                    <h2 style="text-align: center;">${this.getDocumentTitle(type)}</h2>
                    
                    <p>التاريخ: ${date}</p>
                    
                    <p>الخاص بالسيد/السيدة: <strong>${member.fullName}</strong></p>
                    <p>رقم المجموعة: <strong>${member.groupNumber}</strong></p>
                    <p>البناء/الشقة: <strong>${member.buildingApt}</strong></p>
                    
                    ${notes ? `<p><strong>التفاصيل:</strong> ${notes}</p>` : ''}
                    
                    <div style="margin-top: 50px;">
                        <p>رئيس النقابة</p>
                        <p>التوقيع: ________________</p>
                        <p>الختم: ________________</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Show document modal
     */
    showDocumentModal(content) {
        const modalHtml = `
            <div class="modal-overlay" id="document-modal">
                <div class="modal large-modal">
                    <div class="modal-header">
                        <h3>وثيقة قانونية</h3>
                        <div class="modal-actions">
                            <button type="button" class="btn btn-outline" onclick="courtDocsManager.printDocument()">
                                <i class="bi bi-printer"></i>
                                طباعة
                            </button>
                            <button type="button" class="btn-icon" onclick="courtDocsManager.closeDocumentModal()">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        document.body.classList.add('modal-open');
    }

    /**
     * Print document
     */
    printDocument() {
        const documentContent = document.querySelector('#document-modal .document-content');
        if (!documentContent) return;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>وثيقة قانونية</title>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            direction: rtl; 
                            text-align: right; 
                            padding: 20px;
                            line-height: 1.6;
                        }
                        .document-header { text-align: center; margin-bottom: 30px; }
                        .document-body { margin: 20px 0; }
                        h1, h2 { color: #333; }
                        p { margin: 10px 0; }
                    </style>
                </head>
                <body>
                    ${documentContent.outerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    /**
     * Close document modal
     */
    closeDocumentModal() {
        const modal = document.getElementById('document-modal');
        if (modal) {
            modal.remove();
            document.body.classList.remove('modal-open');
        }
    }

    /**
     * Close court doc modal
     */
    closeCourtDocModal() {
        const modal = document.getElementById('court-doc-modal');
        if (modal) {
            modal.remove();
            document.body.classList.remove('modal-open');
        }
    }

    /**
     * Get document title
     */
    getDocumentTitle(type) {
        const titles = {
            notice_of_dues: 'إشعار الاستحقاق',
            ownership_certificate: 'شهادة الملكية',
            court_order_request: 'طلب أمر قضائي',
            precautionary_seizure: 'أمر الحجز الاحتياطي',
            legal_status: 'الوضعية القانونية للمالك',
            judgment_registration: 'تسجيل الأحكام القضائية'
        };
        return titles[type] || type;
    }
}

// Create global instances
window.reportsManager = new ReportsManager();
window.courtDocsManager = new CourtDocsManager();

// Global functions for HTML onclick handlers
function generateReport(type) {
    window.reportsManager.generateReport(type);
}

function createCourtDoc(type) {
    window.courtDocsManager.createCourtDoc(type);
}
