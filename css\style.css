/* ===== CSS Variables ===== */
:root {
    /* Neumorphic Colors */
    --bg-primary: #e6e7ee;
    --bg-secondary: #f0f0f3;
    --shadow-light: #ffffff;
    --shadow-dark: #d1d9e6;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --accent-primary: #3498db;
    --accent-secondary: #2980b9;
    --success: #27ae60;
    --warning: #f39c12;
    --danger: #e74c3c;
    
    /* Dark Theme Colors */
    --dark-bg-primary: #2c2c54;
    --dark-bg-secondary: #40407a;
    --dark-shadow-light: #3c3c6e;
    --dark-shadow-dark: #1e1e3f;
    --dark-text-primary: #ffffff;
    --dark-text-secondary: #bdc3c7;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Shadows */
    --shadow-neumorphic: 
        6px 6px 12px var(--shadow-dark),
        -6px -6px 12px var(--shadow-light);
    --shadow-neumorphic-inset: 
        inset 6px 6px 12px var(--shadow-dark),
        inset -6px -6px 12px var(--shadow-light);
    --shadow-neumorphic-hover: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

/* ===== Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: all var(--transition-normal);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

/* ===== Language Toggle ===== */
.language-toggle {
    position: fixed;
    top: var(--spacing-md);
    right: var(--spacing-md);
    z-index: 1000;
    display: flex;
    gap: var(--spacing-xs);
}

.lang-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-neumorphic);
}

.lang-btn:hover {
    box-shadow: var(--shadow-neumorphic-hover);
    transform: translateY(-2px);
}

.lang-btn.active {
    background: var(--accent-primary);
    color: white;
    box-shadow: var(--shadow-neumorphic-inset);
}

/* ===== Header ===== */
.header {
    background: var(--bg-secondary);
    padding: var(--spacing-lg) 0;
    box-shadow: var(--shadow-neumorphic);
    margin-bottom: var(--spacing-xl);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    color: var(--accent-primary);
    font-size: 1.75rem;
    font-weight: 700;
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* ===== Buttons ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    background: var(--bg-secondary);
    color: var(--text-primary);
    box-shadow: var(--shadow-neumorphic);
}

.btn:hover {
    box-shadow: var(--shadow-neumorphic-hover);
    transform: translateY(-2px);
}

.btn:active {
    box-shadow: var(--shadow-neumorphic-inset);
    transform: translateY(0);
}

.btn-primary {
    background: var(--accent-primary);
    color: white;
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--accent-primary);
    color: var(--accent-primary);
}

.btn-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

/* ===== Sidebar Navigation ===== */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 280px;
    height: 100vh;
    background: var(--bg-secondary);
    box-shadow: var(--shadow-neumorphic);
    padding: var(--spacing-xl) 0;
    z-index: 999;
    transition: transform var(--transition-normal);
}

.nav-header {
    padding: 0 var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-primary);
    text-decoration: none;
    transition: all var(--transition-normal);
    margin: 0 var(--spacing-md);
    border-radius: var(--radius-md);
}

.nav-link:hover {
    background: var(--bg-primary);
    box-shadow: var(--shadow-neumorphic-inset);
}

.nav-link.active {
    background: var(--accent-primary);
    color: white;
    box-shadow: var(--shadow-neumorphic);
}

.nav-link i {
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
}

/* ===== Main Content ===== */
.main-content {
    margin-left: 280px;
    min-height: 100vh;
    padding: var(--spacing-xl);
    transition: margin-left var(--transition-normal);
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.page-header h2 {
    margin-bottom: 0;
}

/* ===== Cards ===== */
.card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-neumorphic);
    margin-bottom: var(--spacing-xl);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--shadow-dark);
}

.card-body {
    padding: var(--spacing-lg);
}

/* ===== Stats Grid ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-neumorphic);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    box-shadow: var(--shadow-neumorphic-hover);
    transform: translateY(-4px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--accent-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: var(--shadow-neumorphic);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-content p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* ===== Loading Overlay ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(230, 231, 238, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-neumorphic);
    animation: spin 1s linear infinite;
}

.loading-spinner i {
    font-size: 1.5rem;
    color: var(--accent-primary);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Search and Filters ===== */
.filters-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    gap: var(--spacing-lg);
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-box i {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    z-index: 1;
}

.search-box input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 40px;
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    color: var(--text-primary);
    box-shadow: var(--shadow-neumorphic-inset);
    transition: all var(--transition-normal);
}

.search-box input:focus {
    outline: none;
    box-shadow: var(--shadow-neumorphic-inset), 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.filter-buttons {
    display: flex;
    gap: var(--spacing-sm);
}

.filter-buttons .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
}

.filter-buttons .btn.active {
    background: var(--accent-primary);
    color: white;
    box-shadow: var(--shadow-neumorphic-inset);
}

/* ===== Tables ===== */
.table-responsive {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-neumorphic-inset);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-secondary);
}

.table th,
.table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--shadow-dark);
}

.table th {
    background: var(--bg-primary);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 1;
}

.table tbody tr {
    transition: all var(--transition-fast);
}

.table tbody tr:hover {
    background: var(--bg-primary);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.text-center {
    text-align: center;
}

/* ===== Status Badges ===== */
.status-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-active {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success);
    border: 1px solid rgba(39, 174, 96, 0.2);
}

.status-overdue {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger);
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.status-pending {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning);
    border: 1px solid rgba(243, 156, 18, 0.2);
}

/* ===== Action Buttons ===== */
.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
}

.btn-sm {
    width: 32px;
    height: 32px;
    padding: 0;
    font-size: 0.875rem;
}

.btn-danger {
    background: var(--danger);
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

/* ===== Activities List ===== */
.activities-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
    transition: all var(--transition-normal);
}

.activity-item:hover {
    background: var(--bg-primary);
    box-shadow: var(--shadow-neumorphic-inset);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--accent-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
    box-shadow: var(--shadow-neumorphic);
}

.activity-content {
    flex: 1;
}

.activity-content p {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: 0.875rem;
    color: var(--text-primary);
}

.activity-content small {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* ===== Modals ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

.modal {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-neumorphic);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--shadow-dark);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    flex: 1;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--shadow-dark);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

.modal-open {
    overflow: hidden;
}

/* ===== Forms ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    box-shadow: var(--shadow-neumorphic-inset);
    transition: all var(--transition-normal);
    font-size: 0.875rem;
}

.form-control:focus {
    outline: none;
    box-shadow: var(--shadow-neumorphic-inset), 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control::placeholder {
    color: var(--text-secondary);
}

.form-row {
    display: flex;
    gap: var(--spacing-md);
}

.form-row .form-group {
    flex: 1;
}

textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

/* ===== Checkboxes ===== */
.subscription-years {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);
    max-height: 200px;
    overflow-y: auto;
    padding: var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-neumorphic-inset);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: 0.875rem;
}

.checkbox-label:hover {
    background: var(--bg-secondary);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 18px;
    height: 18px;
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
    box-shadow: var(--shadow-neumorphic-inset);
    position: relative;
    transition: all var(--transition-normal);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background: var(--accent-primary);
    box-shadow: var(--shadow-neumorphic);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        padding: var(--spacing-md);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .page-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .filters-section {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .filter-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .form-row {
        flex-direction: column;
    }

    .modal {
        width: 95%;
        margin: var(--spacing-md);
    }

    .subscription-years {
        grid-template-columns: 1fr;
    }
}

/* ===== Subscriptions Grid ===== */
.subscriptions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.subscription-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-neumorphic);
    transition: all var(--transition-normal);
}

.subscription-card:hover {
    box-shadow: var(--shadow-neumorphic-hover);
    transform: translateY(-4px);
}

.subscription-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.subscription-year {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-primary);
}

.subscription-amount {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--success);
}

.subscription-info {
    margin-bottom: var(--spacing-md);
}

.subscription-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* ===== Reports Grid ===== */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.report-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-neumorphic);
    text-align: center;
    transition: all var(--transition-normal);
}

.report-card:hover {
    box-shadow: var(--shadow-neumorphic-hover);
    transform: translateY(-4px);
}

.report-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--accent-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    color: white;
    font-size: 2rem;
    box-shadow: var(--shadow-neumorphic);
}

.report-content h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.report-content p {
    margin-bottom: var(--spacing-lg);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* ===== Court Documents Grid ===== */
.court-docs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.doc-type-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-neumorphic);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.doc-type-card:hover {
    box-shadow: var(--shadow-neumorphic-hover);
    transform: translateY(-4px);
}

.doc-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--accent-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    color: white;
    font-size: 1.5rem;
    box-shadow: var(--shadow-neumorphic);
}

.doc-type-card h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

/* ===== Settings Grid ===== */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.data-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.data-actions .btn {
    justify-content: flex-start;
}

/* ===== Dark Theme ===== */
.dark-theme {
    --bg-primary: var(--dark-bg-primary);
    --bg-secondary: var(--dark-bg-secondary);
    --shadow-light: var(--dark-shadow-light);
    --shadow-dark: var(--dark-shadow-dark);
    --text-primary: var(--dark-text-primary);
    --text-secondary: var(--dark-text-secondary);
}

.dark-theme .loading-overlay {
    background: rgba(44, 44, 84, 0.8);
}

/* ===== Utility Classes ===== */
.text-muted {
    color: var(--text-secondary);
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* ===== Payment Method Badges ===== */
.payment-method-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.payment-cash {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success);
    border: 1px solid rgba(39, 174, 96, 0.2);
}

.payment-check {
    background: rgba(52, 152, 219, 0.1);
    color: var(--accent-primary);
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.payment-bank_transfer {
    background: rgba(155, 89, 182, 0.1);
    color: #9b59b6;
    border: 1px solid rgba(155, 89, 182, 0.2);
}

/* ===== Subscription Selection ===== */
.subscription-checkboxes {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-height: 200px;
    overflow-y: auto;
    padding: var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-neumorphic-inset);
}

/* ===== Print Styles ===== */
@media print {
    body {
        background: white;
        color: black;
    }

    .sidebar,
    .header,
    .language-toggle,
    .btn,
    .action-buttons {
        display: none !important;
    }

    .main-content {
        margin: 0;
        padding: 0;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .table {
        border-collapse: collapse;
    }

    .table th,
    .table td {
        border: 1px solid #ddd;
        padding: 8px;
    }
}

/* ===== Additional Form Styles ===== */
.form-control:disabled,
.form-control[readonly] {
    background-color: var(--bg-primary);
    opacity: 0.7;
    cursor: not-allowed;
}

select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 40px;
    appearance: none;
}

/* ===== Responsive Improvements ===== */
@media (max-width: 768px) {
    .reports-grid,
    .court-docs-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }

    .data-actions {
        flex-direction: column;
    }

    .subscription-card,
    .report-card,
    .doc-type-card {
        text-align: center;
    }

    .subscription-actions {
        justify-content: center;
    }
}

/* ===== Animation Enhancements ===== */
.card,
.stat-card,
.subscription-card,
.report-card,
.doc-type-card {
    transition: all var(--transition-normal);
}

.card:hover,
.stat-card:hover,
.subscription-card:hover,
.report-card:hover,
.doc-type-card:hover {
    transform: translateY(-2px);
}

/* ===== Focus Styles ===== */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* ===== Loading States ===== */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* ===== Notifications ===== */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-neumorphic);
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    max-width: 400px;
    min-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.notification-close:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.notification-success {
    border-left: 4px solid var(--success);
}

.notification-success .notification-content i {
    color: var(--success);
}

.notification-error {
    border-left: 4px solid var(--danger);
}

.notification-error .notification-content i {
    color: var(--danger);
}

.notification-warning {
    border-left: 4px solid var(--warning);
}

.notification-warning .notification-content i {
    color: var(--warning);
}

.notification-info {
    border-left: 4px solid var(--accent-primary);
}

.notification-info .notification-content i {
    color: var(--accent-primary);
}

/* ===== Large Modal ===== */
.large-modal {
    max-width: 90%;
    width: 1000px;
}

.modal-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* ===== Report Styles ===== */
.report-content {
    font-family: Arial, sans-serif;
    line-height: 1.6;
}

.report-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--shadow-dark);
}

.report-header h1 {
    color: var(--accent-primary);
    margin-bottom: var(--spacing-sm);
}

.report-header h2 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.report-summary,
.report-details {
    margin-bottom: var(--spacing-xl);
}

.summary-table,
.details-table {
    width: 100%;
    border-collapse: collapse;
    margin: var(--spacing-md) 0;
}

.summary-table th,
.summary-table td,
.details-table th,
.details-table td {
    border: 1px solid var(--shadow-dark);
    padding: var(--spacing-sm);
    text-align: right;
}

.summary-table th,
.details-table th {
    background: var(--bg-primary);
    font-weight: 600;
}

.summary-table tr:nth-child(even),
.details-table tr:nth-child(even) {
    background: var(--bg-primary);
}

/* ===== Document Styles ===== */
.document-content {
    font-family: Arial, sans-serif;
    line-height: 1.8;
    color: var(--text-primary);
}

.document-header {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--shadow-dark);
}

.document-header h1 {
    color: var(--accent-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 1.8rem;
}

.document-body {
    margin: var(--spacing-xl) 0;
}

.document-body h2 {
    color: var(--text-primary);
    margin: var(--spacing-xl) 0 var(--spacing-lg);
    text-decoration: underline;
}

.document-body p {
    margin: var(--spacing-md) 0;
}

/* ===== RTL Notifications ===== */
[dir="rtl"] .notification {
    right: auto;
    left: 20px;
    transform: translateX(-100%);
}

[dir="rtl"] .notification.show {
    transform: translateX(0);
}

[dir="rtl"] .notification-success,
[dir="rtl"] .notification-error,
[dir="rtl"] .notification-warning,
[dir="rtl"] .notification-info {
    border-left: none;
    border-right: 4px solid;
}
