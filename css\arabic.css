/* ===== Arabic RTL Styles ===== */
[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

/* ===== RTL Layout Adjustments ===== */
[dir="rtl"] .language-toggle {
    right: auto;
    left: var(--spacing-md);
}

[dir="rtl"] .sidebar {
    left: auto;
    right: 0;
}

[dir="rtl"] .main-content {
    margin-left: 0;
    margin-right: 280px;
}

[dir="rtl"] .nav-link {
    flex-direction: row-reverse;
}

[dir="rtl"] .nav-link i {
    margin-left: 0;
    margin-right: var(--spacing-md);
}

[dir="rtl"] .page-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .stat-card {
    flex-direction: row-reverse;
}

[dir="rtl"] .btn {
    flex-direction: row-reverse;
}

[dir="rtl"] .search-box {
    flex-direction: row-reverse;
}

[dir="rtl"] .search-box i {
    right: auto;
    left: var(--spacing-md);
}

[dir="rtl"] .search-box input {
    padding-right: var(--spacing-md);
    padding-left: 40px;
}

[dir="rtl"] .activity-item {
    flex-direction: row-reverse;
}

[dir="rtl"] .activity-icon {
    margin-left: 0;
    margin-right: var(--spacing-md);
}

/* ===== Arabic Typography ===== */
[dir="rtl"] body {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

[dir="rtl"] h1, 
[dir="rtl"] h2, 
[dir="rtl"] h3, 
[dir="rtl"] h4, 
[dir="rtl"] h5, 
[dir="rtl"] h6 {
    font-weight: 600;
}

/* ===== Table RTL ===== */
[dir="rtl"] .table th,
[dir="rtl"] .table td {
    text-align: right;
}

[dir="rtl"] .table th:first-child,
[dir="rtl"] .table td:first-child {
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

[dir="rtl"] .table th:last-child,
[dir="rtl"] .table td:last-child {
    border-radius: var(--radius-sm) 0 0 var(--radius-sm);
}

/* ===== Form RTL ===== */
[dir="rtl"] .form-group label {
    text-align: right;
}

[dir="rtl"] .form-control {
    text-align: right;
}

[dir="rtl"] .form-row {
    flex-direction: row-reverse;
}

/* ===== Modal RTL ===== */
[dir="rtl"] .modal-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .modal-header .close {
    margin-left: 0;
    margin-right: auto;
}

/* ===== Responsive RTL ===== */
@media (max-width: 768px) {
    [dir="rtl"] .sidebar {
        transform: translateX(100%);
    }
    
    [dir="rtl"] .sidebar.open {
        transform: translateX(0);
    }
    
    [dir="rtl"] .main-content {
        margin-right: 0;
    }
}

/* ===== Arabic Number Formatting ===== */
[dir="rtl"] .arabic-numbers {
    font-family: 'Arial', sans-serif;
    direction: ltr;
    display: inline-block;
}

/* ===== Specific Arabic UI Elements ===== */
[dir="rtl"] .breadcrumb {
    flex-direction: row-reverse;
}

[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    content: "\\";
    padding-right: 0;
    padding-left: var(--spacing-sm);
}

[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
}

[dir="rtl"] .toast {
    right: var(--spacing-md);
    left: auto;
}

/* ===== Arabic Text Improvements ===== */
[dir="rtl"] {
    line-height: 1.8;
    letter-spacing: 0.02em;
}

[dir="rtl"] .text-large {
    font-size: 1.125rem;
    line-height: 1.9;
}

[dir="rtl"] .text-small {
    font-size: 0.875rem;
    line-height: 1.7;
}

/* ===== Arabic Form Validation ===== */
[dir="rtl"] .form-control.is-invalid {
    border-color: var(--danger);
    box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
}

[dir="rtl"] .form-control.is-valid {
    border-color: var(--success);
    box-shadow: 0 0 0 0.2rem rgba(39, 174, 96, 0.25);
}

[dir="rtl"] .invalid-feedback,
[dir="rtl"] .valid-feedback {
    text-align: right;
    margin-top: var(--spacing-xs);
}

/* ===== Arabic Calendar Adjustments ===== */
[dir="rtl"] .calendar {
    direction: rtl;
}

[dir="rtl"] .calendar-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .calendar-nav {
    flex-direction: row-reverse;
}

/* ===== Arabic Print Styles ===== */
@media print {
    [dir="rtl"] body {
        direction: rtl;
        text-align: right;
    }
    
    [dir="rtl"] .sidebar {
        display: none;
    }
    
    [dir="rtl"] .main-content {
        margin-right: 0;
        margin-left: 0;
    }
}
