/**
 * Local Storage Management System
 * Handles all data persistence using localStorage
 */

class StorageManager {
    constructor() {
        this.storageKeys = {
            MEMBERS: 'syndic_members',
            SUBSCRIPTIONS: 'syndic_subscriptions',
            RECEIPTS: 'syndic_receipts',
            COURT_DOCS: 'syndic_court_docs',
            SETTINGS: 'syndic_settings',
            ACTIVITIES: 'syndic_activities',
            LANGUAGE: 'syndic_language',
            THEME: 'syndic_theme'
        };
        
        this.initializeStorage();
    }

    /**
     * Initialize storage with default data if empty
     */
    initializeStorage() {
        // Initialize members if not exists
        if (!this.getMembers()) {
            this.setItem(this.storageKeys.MEMBERS, []);
        }

        // Initialize subscriptions if not exists
        if (!this.getSubscriptions()) {
            this.setItem(this.storageKeys.SUBSCRIPTIONS, this.getDefaultSubscriptions());
        }

        // Initialize receipts if not exists
        if (!this.getReceipts()) {
            this.setItem(this.storageKeys.RECEIPTS, []);
        }

        // Initialize court documents if not exists
        if (!this.getCourtDocs()) {
            this.setItem(this.storageKeys.COURT_DOCS, []);
        }

        // Initialize settings if not exists
        if (!this.getSettings()) {
            this.setItem(this.storageKeys.SETTINGS, this.getDefaultSettings());
        }

        // Initialize activities if not exists
        if (!this.getActivities()) {
            this.setItem(this.storageKeys.ACTIVITIES, []);
        }
    }

    /**
     * Generic localStorage methods
     */
    setItem(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('Error saving to localStorage:', error);
            return false;
        }
    }

    getItem(key) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return null;
        }
    }

    removeItem(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('Error removing from localStorage:', error);
            return false;
        }
    }

    /**
     * Members Management
     */
    getMembers() {
        return this.getItem(this.storageKeys.MEMBERS) || [];
    }

    saveMember(member) {
        const members = this.getMembers();
        
        if (member.id) {
            // Update existing member
            const index = members.findIndex(m => m.id === member.id);
            if (index !== -1) {
                members[index] = { ...member, updatedAt: new Date().toISOString() };
            }
        } else {
            // Add new member
            member.id = this.generateId();
            member.createdAt = new Date().toISOString();
            member.updatedAt = new Date().toISOString();
            members.push(member);
        }

        this.setItem(this.storageKeys.MEMBERS, members);
        this.addActivity('member', member.id ? 'updated' : 'created', member.fullName);
        return member;
    }

    deleteMember(memberId) {
        const members = this.getMembers();
        const memberIndex = members.findIndex(m => m.id === memberId);
        
        if (memberIndex !== -1) {
            const member = members[memberIndex];
            members.splice(memberIndex, 1);
            this.setItem(this.storageKeys.MEMBERS, members);
            this.addActivity('member', 'deleted', member.fullName);
            return true;
        }
        return false;
    }

    getMemberById(memberId) {
        const members = this.getMembers();
        return members.find(m => m.id === memberId);
    }

    /**
     * Subscriptions Management
     */
    getSubscriptions() {
        return this.getItem(this.storageKeys.SUBSCRIPTIONS) || [];
    }

    saveSubscription(subscription) {
        const subscriptions = this.getSubscriptions();
        
        if (subscription.id) {
            // Update existing subscription
            const index = subscriptions.findIndex(s => s.id === subscription.id);
            if (index !== -1) {
                subscriptions[index] = { ...subscription, updatedAt: new Date().toISOString() };
            }
        } else {
            // Add new subscription
            subscription.id = this.generateId();
            subscription.createdAt = new Date().toISOString();
            subscription.updatedAt = new Date().toISOString();
            subscriptions.push(subscription);
        }

        this.setItem(this.storageKeys.SUBSCRIPTIONS, subscriptions);
        this.addActivity('subscription', subscription.id ? 'updated' : 'created', subscription.name);
        return subscription;
    }

    deleteSubscription(subscriptionId) {
        const subscriptions = this.getSubscriptions();
        const subscriptionIndex = subscriptions.findIndex(s => s.id === subscriptionId);
        
        if (subscriptionIndex !== -1) {
            const subscription = subscriptions[subscriptionIndex];
            subscriptions.splice(subscriptionIndex, 1);
            this.setItem(this.storageKeys.SUBSCRIPTIONS, subscriptions);
            this.addActivity('subscription', 'deleted', subscription.name);
            return true;
        }
        return false;
    }

    getDefaultSubscriptions() {
        const currentYear = new Date().getFullYear();
        const subscriptions = [];
        
        for (let year = 2018; year <= 2032; year++) {
            subscriptions.push({
                id: this.generateId(),
                year: year,
                name: `اشتراك ${year}`,
                amount: 5000, // Default amount in DZD
                dueDate: `${year}-12-31`,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            });
        }
        
        return subscriptions;
    }

    /**
     * Receipts Management
     */
    getReceipts() {
        return this.getItem(this.storageKeys.RECEIPTS) || [];
    }

    saveReceipt(receipt) {
        const receipts = this.getReceipts();
        
        if (receipt.id) {
            // Update existing receipt
            const index = receipts.findIndex(r => r.id === receipt.id);
            if (index !== -1) {
                receipts[index] = { ...receipt, updatedAt: new Date().toISOString() };
            }
        } else {
            // Add new receipt
            receipt.id = this.generateId();
            receipt.receiptNumber = this.generateReceiptNumber();
            receipt.createdAt = new Date().toISOString();
            receipt.updatedAt = new Date().toISOString();
            receipts.push(receipt);
        }

        this.setItem(this.storageKeys.RECEIPTS, receipts);
        this.addActivity('receipt', receipt.id ? 'updated' : 'created', receipt.receiptNumber);
        return receipt;
    }

    /**
     * Court Documents Management
     */
    getCourtDocs() {
        return this.getItem(this.storageKeys.COURT_DOCS) || [];
    }

    saveCourtDoc(courtDoc) {
        const courtDocs = this.getCourtDocs();
        
        if (courtDoc.id) {
            // Update existing document
            const index = courtDocs.findIndex(d => d.id === courtDoc.id);
            if (index !== -1) {
                courtDocs[index] = { ...courtDoc, updatedAt: new Date().toISOString() };
            }
        } else {
            // Add new document
            courtDoc.id = this.generateId();
            courtDoc.createdAt = new Date().toISOString();
            courtDoc.updatedAt = new Date().toISOString();
            courtDocs.push(courtDoc);
        }

        this.setItem(this.storageKeys.COURT_DOCS, courtDocs);
        this.addActivity('court_doc', courtDoc.id ? 'updated' : 'created', courtDoc.title);
        return courtDoc;
    }

    /**
     * Settings Management
     */
    getSettings() {
        return this.getItem(this.storageKeys.SETTINGS) || this.getDefaultSettings();
    }

    saveSettings(settings) {
        const currentSettings = this.getSettings();
        const updatedSettings = { ...currentSettings, ...settings, updatedAt: new Date().toISOString() };
        this.setItem(this.storageKeys.SETTINGS, updatedSettings);
        return updatedSettings;
    }

    getDefaultSettings() {
        return {
            language: 'ar',
            theme: 'light',
            currency: 'DZD',
            dateFormat: 'DD/MM/YYYY',
            organizationName: 'نقابة الملاك',
            organizationAddress: '',
            organizationPhone: '',
            organizationEmail: '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
    }

    /**
     * Activities Management
     */
    getActivities() {
        return this.getItem(this.storageKeys.ACTIVITIES) || [];
    }

    addActivity(type, action, target) {
        const activities = this.getActivities();
        const activity = {
            id: this.generateId(),
            type: type,
            action: action,
            target: target,
            timestamp: new Date().toISOString()
        };
        
        activities.unshift(activity); // Add to beginning
        
        // Keep only last 100 activities
        if (activities.length > 100) {
            activities.splice(100);
        }
        
        this.setItem(this.storageKeys.ACTIVITIES, activities);
        return activity;
    }

    /**
     * Utility Methods
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    generateReceiptNumber() {
        const receipts = this.getReceipts();
        const currentYear = new Date().getFullYear();
        const yearReceipts = receipts.filter(r => r.receiptNumber && r.receiptNumber.includes(currentYear.toString()));
        const nextNumber = yearReceipts.length + 1;
        return `${currentYear}-${nextNumber.toString().padStart(4, '0')}`;
    }

    /**
     * Data Export/Import
     */
    exportData() {
        const data = {
            members: this.getMembers(),
            subscriptions: this.getSubscriptions(),
            receipts: this.getReceipts(),
            courtDocs: this.getCourtDocs(),
            settings: this.getSettings(),
            activities: this.getActivities(),
            exportDate: new Date().toISOString()
        };
        
        return JSON.stringify(data, null, 2);
    }

    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            if (data.members) this.setItem(this.storageKeys.MEMBERS, data.members);
            if (data.subscriptions) this.setItem(this.storageKeys.SUBSCRIPTIONS, data.subscriptions);
            if (data.receipts) this.setItem(this.storageKeys.RECEIPTS, data.receipts);
            if (data.courtDocs) this.setItem(this.storageKeys.COURT_DOCS, data.courtDocs);
            if (data.settings) this.setItem(this.storageKeys.SETTINGS, data.settings);
            if (data.activities) this.setItem(this.storageKeys.ACTIVITIES, data.activities);
            
            this.addActivity('system', 'data_imported', 'Data imported successfully');
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }

    /**
     * Clear all data
     */
    clearAllData() {
        Object.values(this.storageKeys).forEach(key => {
            this.removeItem(key);
        });
        this.initializeStorage();
    }
}

// Create global storage instance
window.storage = new StorageManager();
