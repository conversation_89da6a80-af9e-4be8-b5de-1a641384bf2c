/* ===== French LTR Styles ===== */
[dir="ltr"] {
    direction: ltr;
    text-align: left;
}

/* ===== LTR Layout Adjustments ===== */
[dir="ltr"] .language-toggle {
    right: var(--spacing-md);
    left: auto;
}

[dir="ltr"] .sidebar {
    left: 0;
    right: auto;
}

[dir="ltr"] .main-content {
    margin-left: 280px;
    margin-right: 0;
}

[dir="ltr"] .nav-link {
    flex-direction: row;
}

[dir="ltr"] .nav-link i {
    margin-right: var(--spacing-md);
    margin-left: 0;
}

[dir="ltr"] .page-header {
    flex-direction: row;
}

[dir="ltr"] .stat-card {
    flex-direction: row;
}

[dir="ltr"] .btn {
    flex-direction: row;
}

[dir="ltr"] .search-box {
    flex-direction: row;
}

[dir="ltr"] .search-box i {
    left: var(--spacing-md);
    right: auto;
}

[dir="ltr"] .search-box input {
    padding-left: 40px;
    padding-right: var(--spacing-md);
}

[dir="ltr"] .activity-item {
    flex-direction: row;
}

[dir="ltr"] .activity-icon {
    margin-right: var(--spacing-md);
    margin-left: 0;
}

/* ===== French Typography ===== */
[dir="ltr"] body {
    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

[dir="ltr"] h1, 
[dir="ltr"] h2, 
[dir="ltr"] h3, 
[dir="ltr"] h4, 
[dir="ltr"] h5, 
[dir="ltr"] h6 {
    font-weight: 600;
    letter-spacing: -0.02em;
}

/* ===== Table LTR ===== */
[dir="ltr"] .table th,
[dir="ltr"] .table td {
    text-align: left;
}

[dir="ltr"] .table th:first-child,
[dir="ltr"] .table td:first-child {
    border-radius: var(--radius-sm) 0 0 var(--radius-sm);
}

[dir="ltr"] .table th:last-child,
[dir="ltr"] .table td:last-child {
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

/* ===== Form LTR ===== */
[dir="ltr"] .form-group label {
    text-align: left;
}

[dir="ltr"] .form-control {
    text-align: left;
}

[dir="ltr"] .form-row {
    flex-direction: row;
}

/* ===== Modal LTR ===== */
[dir="ltr"] .modal-header {
    flex-direction: row;
}

[dir="ltr"] .modal-header .close {
    margin-right: 0;
    margin-left: auto;
}

/* ===== Responsive LTR ===== */
@media (max-width: 768px) {
    [dir="ltr"] .sidebar {
        transform: translateX(-100%);
    }
    
    [dir="ltr"] .sidebar.open {
        transform: translateX(0);
    }
    
    [dir="ltr"] .main-content {
        margin-left: 0;
    }
}

/* ===== French Number Formatting ===== */
[dir="ltr"] .french-numbers {
    font-family: 'Roboto', 'Arial', sans-serif;
}

/* ===== Specific French UI Elements ===== */
[dir="ltr"] .breadcrumb {
    flex-direction: row;
}

[dir="ltr"] .breadcrumb-item + .breadcrumb-item::before {
    content: "/";
    padding-left: 0;
    padding-right: var(--spacing-sm);
}

[dir="ltr"] .dropdown-menu {
    left: 0;
    right: auto;
}

[dir="ltr"] .toast {
    left: var(--spacing-md);
    right: auto;
}

/* ===== French Text Improvements ===== */
[dir="ltr"] {
    line-height: 1.6;
    letter-spacing: 0;
}

[dir="ltr"] .text-large {
    font-size: 1.125rem;
    line-height: 1.7;
}

[dir="ltr"] .text-small {
    font-size: 0.875rem;
    line-height: 1.5;
}

/* ===== French Form Validation ===== */
[dir="ltr"] .form-control.is-invalid {
    border-color: var(--danger);
    box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
}

[dir="ltr"] .form-control.is-valid {
    border-color: var(--success);
    box-shadow: 0 0 0 0.2rem rgba(39, 174, 96, 0.25);
}

[dir="ltr"] .invalid-feedback,
[dir="ltr"] .valid-feedback {
    text-align: left;
    margin-top: var(--spacing-xs);
}

/* ===== French Calendar Adjustments ===== */
[dir="ltr"] .calendar {
    direction: ltr;
}

[dir="ltr"] .calendar-header {
    flex-direction: row;
}

[dir="ltr"] .calendar-nav {
    flex-direction: row;
}

/* ===== French Specific Styling ===== */
[dir="ltr"] .french-accent {
    font-style: italic;
    color: var(--accent-secondary);
}

[dir="ltr"] .french-quote::before {
    content: "« ";
}

[dir="ltr"] .french-quote::after {
    content: " »";
}

/* ===== French Print Styles ===== */
@media print {
    [dir="ltr"] body {
        direction: ltr;
        text-align: left;
    }
    
    [dir="ltr"] .sidebar {
        display: none;
    }
    
    [dir="ltr"] .main-content {
        margin-left: 0;
        margin-right: 0;
    }
}

/* ===== French Currency Formatting ===== */
[dir="ltr"] .currency-euro::after {
    content: " €";
}

[dir="ltr"] .currency-dinar::after {
    content: " DA";
}

/* ===== French Date Formatting ===== */
[dir="ltr"] .date-french {
    font-variant-numeric: oldstyle-nums;
}

/* ===== French Typography Enhancements ===== */
[dir="ltr"] .french-title {
    font-family: 'Georgia', 'Times New Roman', serif;
    font-weight: 400;
    letter-spacing: 0.05em;
}

[dir="ltr"] .french-subtitle {
    font-style: italic;
    color: var(--text-secondary);
    font-size: 0.9em;
}
