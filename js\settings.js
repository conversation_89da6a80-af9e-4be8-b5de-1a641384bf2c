/**
 * Settings Management Functions
 * Global functions for settings operations
 */

/**
 * Save organization settings
 */
function saveOrganizationSettings() {
    const orgName = document.getElementById('org-name').value.trim();
    const orgAddress = document.getElementById('org-address').value.trim();
    const orgPhone = document.getElementById('org-phone').value.trim();
    const orgEmail = document.getElementById('org-email').value.trim();

    // Validate email if provided
    if (orgEmail && !isValidEmail(orgEmail)) {
        alert('البريد الإلكتروني غير صحيح');
        return;
    }

    // Validate phone if provided
    if (orgPhone && !isValidPhone(orgPhone)) {
        alert('رقم الهاتف غير صحيح');
        return;
    }

    const settings = {
        organizationName: orgName,
        organizationAddress: orgAddress,
        organizationPhone: orgPhone,
        organizationEmail: orgEmail
    };

    const savedSettings = window.storage.saveSettings(settings);
    
    if (savedSettings) {
        showNotification('تم حفظ الإعدادات بنجاح', 'success');
        
        // Update header logo if name changed
        const logoElement = document.querySelector('.logo');
        if (logoElement && orgName) {
            logoElement.textContent = orgName;
        }
    }
}

/**
 * Export all data
 */
function exportData() {
    try {
        const data = window.storage.exportData();
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `syndic-data-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showNotification('تم تصدير البيانات بنجاح', 'success');
    } catch (error) {
        console.error('Error exporting data:', error);
        showNotification('حدث خطأ أثناء تصدير البيانات', 'error');
    }
}

/**
 * Import data
 */
function importData() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const jsonData = e.target.result;
                const success = window.storage.importData(jsonData);
                
                if (success) {
                    showNotification('تم استيراد البيانات بنجاح', 'success');
                    
                    // Refresh all managers
                    if (window.membersManager) {
                        window.membersManager.loadMembers();
                        window.membersManager.renderMembersTable();
                    }
                    
                    if (window.subscriptionsManager) {
                        window.subscriptionsManager.loadSubscriptions();
                        window.subscriptionsManager.renderSubscriptionsGrid();
                    }
                    
                    if (window.receiptsManager) {
                        window.receiptsManager.loadReceipts();
                        window.receiptsManager.renderReceiptsTable();
                    }
                    
                    // Update dashboard
                    if (window.syndicApp) {
                        window.syndicApp.updateDashboard();
                    }
                    
                    // Reload settings
                    if (window.syndicApp) {
                        window.syndicApp.loadOrganizationSettings();
                    }
                } else {
                    showNotification('حدث خطأ أثناء استيراد البيانات', 'error');
                }
            } catch (error) {
                console.error('Error importing data:', error);
                showNotification('ملف البيانات غير صحيح', 'error');
            }
        };
        
        reader.readAsText(file);
    };
    
    input.click();
}

/**
 * Clear all data
 */
function clearAllData() {
    const confirmMessage = 'هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.';
    
    if (confirm(confirmMessage)) {
        const doubleConfirm = 'تأكيد أخير: سيتم حذف جميع الأعضاء والاشتراكات والإيصالات والوثائق. هل تريد المتابعة؟';
        
        if (confirm(doubleConfirm)) {
            try {
                window.storage.clearAllData();
                
                // Refresh all managers
                if (window.membersManager) {
                    window.membersManager.loadMembers();
                    window.membersManager.renderMembersTable();
                }
                
                if (window.subscriptionsManager) {
                    window.subscriptionsManager.loadSubscriptions();
                    window.subscriptionsManager.renderSubscriptionsGrid();
                }
                
                if (window.receiptsManager) {
                    window.receiptsManager.loadReceipts();
                    window.receiptsManager.renderReceiptsTable();
                }
                
                // Update dashboard
                if (window.syndicApp) {
                    window.syndicApp.updateDashboard();
                }
                
                // Reload settings
                if (window.syndicApp) {
                    window.syndicApp.loadOrganizationSettings();
                }
                
                showNotification('تم حذف جميع البيانات بنجاح', 'success');
            } catch (error) {
                console.error('Error clearing data:', error);
                showNotification('حدث خطأ أثناء حذف البيانات', 'error');
            }
        }
    }
}

/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Validate phone format (Algerian phone numbers)
 */
function isValidPhone(phone) {
    // Remove spaces and special characters
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    
    // Check for Algerian phone number patterns
    const phoneRegex = /^(\+213|0)(5|6|7)[0-9]{8}$/;
    return phoneRegex.test(cleanPhone);
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="bi bi-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button type="button" class="notification-close" onclick="this.parentElement.remove()">
            <i class="bi bi-x"></i>
        </button>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
    
    // Animate in
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
}

/**
 * Get notification icon based on type
 */
function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * Initialize settings when page loads
 */
document.addEventListener('DOMContentLoaded', function() {
    // Load organization settings when settings section is shown
    const settingsSection = document.getElementById('settings-section');
    if (settingsSection) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (settingsSection.classList.contains('active')) {
                        // Settings section is now active, load settings
                        if (window.syndicApp) {
                            window.syndicApp.loadOrganizationSettings();
                        }
                    }
                }
            });
        });
        
        observer.observe(settingsSection, { attributes: true });
    }
});
