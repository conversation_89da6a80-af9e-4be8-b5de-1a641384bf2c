/**
 * Language Management System
 * Handles Arabic/French bilingual support with RTL/LTR switching
 */

class LanguageManager {
    constructor() {
        this.currentLanguage = 'ar';
        this.translations = {};
        this.loadTranslations();
        this.initializeLanguage();
    }

    /**
     * Load translations for both languages
     */
    loadTranslations() {
        // Arabic translations
        this.translations.ar = {
            // App Title
            app_title: 'نظام إدارة النقابات',
            
            // Navigation
            navigation: 'التنقل',
            dashboard: 'لوحة التحكم',
            members: 'إدارة الأعضاء',
            subscriptions: 'الاشتراكات',
            receipts: 'الإيصالات',
            reports: 'التقارير',
            court_docs: 'الوثائق القانونية',
            settings: 'الإعدادات',
            
            // Dashboard
            dashboard_subtitle: 'نظرة عامة على النظام',
            total_members: 'إجمالي الأعضاء',
            total_dues: 'إجمالي المستحقات',
            overdue_payments: 'المدفوعات المتأخرة',
            receipts_issued: 'الإيصالات المصدرة',
            recent_activities: 'الأنشطة الأخيرة',
            no_activities: 'لا توجد أنشطة حديثة',
            
            // Members
            add_member: 'إضافة عضو',
            search_member: 'البحث عن عضو...',
            all: 'الكل',
            active: 'نشط',
            overdue: 'متأخر',
            full_name: 'الاسم الكامل',
            group_number: 'رقم المجموعة',
            building_apt: 'البناء/الشقة',
            phone: 'الهاتف',
            status: 'الحالة',
            actions: 'الإجراءات',
            no_members: 'لا توجد أعضاء مسجلين',
            
            // Member Form
            member_details: 'بيانات العضو',
            first_name: 'الاسم الأول',
            last_name: 'اسم العائلة',
            email: 'البريد الإلكتروني',
            address: 'العنوان',
            land_title: 'السند العقاري',
            subscription_years: 'سنوات الاشتراك',
            notes: 'ملاحظات',
            save: 'حفظ',
            cancel: 'إلغاء',
            edit: 'تعديل',
            delete: 'حذف',
            view: 'عرض',
            
            // Subscriptions
            subscription_management: 'إدارة الاشتراكات',
            add_subscription: 'إضافة اشتراك',
            subscription_name: 'اسم الاشتراك',
            year: 'السنة',
            amount: 'المبلغ',
            due_date: 'تاريخ الاستحقاق',
            
            // Receipts
            receipt_management: 'إدارة الإيصالات',
            create_receipt: 'إنشاء إيصال',
            receipt_number: 'رقم الإيصال',
            receipt_date: 'تاريخ الإيصال',
            payment_method: 'طريقة الدفع',
            cash: 'نقداً',
            check: 'شيك',
            bank_transfer: 'تحويل بنكي',
            
            // Reports
            daily_report: 'التقرير اليومي',
            monthly_report: 'التقرير الشهري',
            annual_report: 'التقرير السنوي',
            overdue_report: 'تقرير المتأخرات',
            generate_report: 'إنشاء التقرير',
            
            // Court Documents
            court_document_management: 'إدارة الوثائق القانونية',
            add_court_doc: 'إضافة وثيقة قانونية',
            notice_of_dues: 'إشعار الاستحقاق',
            ownership_certificate: 'شهادة الملكية',
            court_order_request: 'طلب أمر قضائي',
            precautionary_seizure: 'أمر الحجز الاحتياطي',
            legal_status: 'الوضعية القانونية للمالك',
            judgment_registration: 'تسجيل الأحكام القضائية',
            
            // Settings
            general_settings: 'الإعدادات العامة',
            organization_info: 'معلومات المؤسسة',
            organization_name: 'اسم المؤسسة',
            organization_address: 'عنوان المؤسسة',
            organization_phone: 'هاتف المؤسسة',
            organization_email: 'بريد المؤسسة الإلكتروني',
            language_settings: 'إعدادات اللغة',
            theme_settings: 'إعدادات المظهر',
            light_theme: 'المظهر الفاتح',
            dark_theme: 'المظهر الداكن',
            
            // Common
            yes: 'نعم',
            no: 'لا',
            confirm: 'تأكيد',
            success: 'نجح',
            error: 'خطأ',
            warning: 'تحذير',
            info: 'معلومات',
            loading: 'جاري التحميل...',
            please_wait: 'يرجى الانتظار...',
            
            // Messages
            member_saved_successfully: 'تم حفظ العضو بنجاح',
            member_deleted_successfully: 'تم حذف العضو بنجاح',
            confirm_delete_member: 'هل أنت متأكد من حذف هذا العضو؟',
            data_exported_successfully: 'تم تصدير البيانات بنجاح',
            data_imported_successfully: 'تم استيراد البيانات بنجاح',
            
            // Validation
            field_required: 'هذا الحقل مطلوب',
            invalid_email: 'البريد الإلكتروني غير صحيح',
            invalid_phone: 'رقم الهاتف غير صحيح',
            invalid_amount: 'المبلغ غير صحيح'
        };

        // French translations
        this.translations.fr = {
            // App Title
            app_title: 'Système de Gestion Syndic',
            
            // Navigation
            navigation: 'Navigation',
            dashboard: 'Tableau de Bord',
            members: 'Gestion des Membres',
            subscriptions: 'Abonnements',
            receipts: 'Reçus',
            reports: 'Rapports',
            court_docs: 'Documents Juridiques',
            settings: 'Paramètres',
            
            // Dashboard
            dashboard_subtitle: 'Vue d\'ensemble du système',
            total_members: 'Total des Membres',
            total_dues: 'Total des Cotisations',
            overdue_payments: 'Paiements en Retard',
            receipts_issued: 'Reçus Émis',
            recent_activities: 'Activités Récentes',
            no_activities: 'Aucune activité récente',
            
            // Members
            add_member: 'Ajouter un Membre',
            search_member: 'Rechercher un membre...',
            all: 'Tous',
            active: 'Actif',
            overdue: 'En Retard',
            full_name: 'Nom Complet',
            group_number: 'Numéro de Groupe',
            building_apt: 'Bâtiment/Appartement',
            phone: 'Téléphone',
            status: 'Statut',
            actions: 'Actions',
            no_members: 'Aucun membre enregistré',
            
            // Member Form
            member_details: 'Détails du Membre',
            first_name: 'Prénom',
            last_name: 'Nom de Famille',
            email: 'Email',
            address: 'Adresse',
            land_title: 'Titre Foncier',
            subscription_years: 'Années d\'Abonnement',
            notes: 'Notes',
            save: 'Enregistrer',
            cancel: 'Annuler',
            edit: 'Modifier',
            delete: 'Supprimer',
            view: 'Voir',
            
            // Subscriptions
            subscription_management: 'Gestion des Abonnements',
            add_subscription: 'Ajouter un Abonnement',
            subscription_name: 'Nom de l\'Abonnement',
            year: 'Année',
            amount: 'Montant',
            due_date: 'Date d\'Échéance',
            
            // Receipts
            receipt_management: 'Gestion des Reçus',
            create_receipt: 'Créer un Reçu',
            receipt_number: 'Numéro de Reçu',
            receipt_date: 'Date du Reçu',
            payment_method: 'Méthode de Paiement',
            cash: 'Espèces',
            check: 'Chèque',
            bank_transfer: 'Virement Bancaire',
            
            // Reports
            daily_report: 'Rapport Quotidien',
            monthly_report: 'Rapport Mensuel',
            annual_report: 'Rapport Annuel',
            overdue_report: 'Rapport des Retards',
            generate_report: 'Générer le Rapport',
            
            // Court Documents
            court_document_management: 'Gestion des Documents Juridiques',
            add_court_doc: 'Ajouter un Document Juridique',
            notice_of_dues: 'Avis d\'Échéance',
            ownership_certificate: 'Certificat de Propriété',
            court_order_request: 'Demande d\'Ordonnance',
            precautionary_seizure: 'Saisie Conservatoire',
            legal_status: 'Statut Juridique du Propriétaire',
            judgment_registration: 'Enregistrement des Jugements',
            
            // Settings
            general_settings: 'Paramètres Généraux',
            organization_info: 'Informations de l\'Organisation',
            organization_name: 'Nom de l\'Organisation',
            organization_address: 'Adresse de l\'Organisation',
            organization_phone: 'Téléphone de l\'Organisation',
            organization_email: 'Email de l\'Organisation',
            language_settings: 'Paramètres de Langue',
            theme_settings: 'Paramètres de Thème',
            light_theme: 'Thème Clair',
            dark_theme: 'Thème Sombre',
            
            // Common
            yes: 'Oui',
            no: 'Non',
            confirm: 'Confirmer',
            success: 'Succès',
            error: 'Erreur',
            warning: 'Avertissement',
            info: 'Information',
            loading: 'Chargement...',
            please_wait: 'Veuillez patienter...',
            
            // Messages
            member_saved_successfully: 'Membre enregistré avec succès',
            member_deleted_successfully: 'Membre supprimé avec succès',
            confirm_delete_member: 'Êtes-vous sûr de vouloir supprimer ce membre ?',
            data_exported_successfully: 'Données exportées avec succès',
            data_imported_successfully: 'Données importées avec succès',
            
            // Validation
            field_required: 'Ce champ est requis',
            invalid_email: 'Email invalide',
            invalid_phone: 'Numéro de téléphone invalide',
            invalid_amount: 'Montant invalide'
        };
    }

    /**
     * Initialize language from storage or default
     */
    initializeLanguage() {
        const savedLanguage = localStorage.getItem('syndic_language');
        if (savedLanguage && (savedLanguage === 'ar' || savedLanguage === 'fr')) {
            this.currentLanguage = savedLanguage;
        }
        this.applyLanguage();
    }

    /**
     * Switch language
     */
    switchLanguage(language) {
        if (language === 'ar' || language === 'fr') {
            this.currentLanguage = language;
            localStorage.setItem('syndic_language', language);
            this.applyLanguage();
        }
    }

    /**
     * Apply language to the entire page
     */
    applyLanguage() {
        const html = document.documentElement;
        const body = document.body;
        
        // Set language and direction
        if (this.currentLanguage === 'ar') {
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
            body.classList.add('rtl');
            body.classList.remove('ltr');
            
            // Enable Arabic styles, disable French styles
            document.getElementById('arabic-styles').disabled = false;
            document.getElementById('french-styles').disabled = true;
        } else {
            html.setAttribute('lang', 'fr');
            html.setAttribute('dir', 'ltr');
            body.classList.add('ltr');
            body.classList.remove('rtl');
            
            // Enable French styles, disable Arabic styles
            document.getElementById('arabic-styles').disabled = true;
            document.getElementById('french-styles').disabled = false;
        }

        // Update language toggle buttons
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.lang === this.currentLanguage) {
                btn.classList.add('active');
            }
        });

        // Translate all elements with data-key attribute
        this.translateElements();
        
        // Translate placeholder attributes
        this.translatePlaceholders();
    }

    /**
     * Translate all elements with data-key attribute
     */
    translateElements() {
        const elements = document.querySelectorAll('[data-key]');
        elements.forEach(element => {
            const key = element.getAttribute('data-key');
            const translation = this.getTranslation(key);
            if (translation) {
                element.textContent = translation;
            }
        });
    }

    /**
     * Translate placeholder attributes
     */
    translatePlaceholders() {
        const elements = document.querySelectorAll('[data-key-placeholder]');
        elements.forEach(element => {
            const key = element.getAttribute('data-key-placeholder');
            const translation = this.getTranslation(key);
            if (translation) {
                element.setAttribute('placeholder', translation);
            }
        });
    }

    /**
     * Get translation for a key
     */
    getTranslation(key) {
        return this.translations[this.currentLanguage][key] || key;
    }

    /**
     * Get current language
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * Check if current language is RTL
     */
    isRTL() {
        return this.currentLanguage === 'ar';
    }

    /**
     * Format number according to current language
     */
    formatNumber(number) {
        if (this.currentLanguage === 'ar') {
            // Arabic number formatting
            return new Intl.NumberFormat('ar-DZ').format(number);
        } else {
            // French number formatting
            return new Intl.NumberFormat('fr-FR').format(number);
        }
    }

    /**
     * Format currency according to current language
     */
    formatCurrency(amount, currency = 'DZD') {
        if (this.currentLanguage === 'ar') {
            return `${this.formatNumber(amount)} د.ج`;
        } else {
            return `${this.formatNumber(amount)} DA`;
        }
    }

    /**
     * Format date according to current language
     */
    formatDate(date) {
        const dateObj = new Date(date);
        if (this.currentLanguage === 'ar') {
            return dateObj.toLocaleDateString('ar-DZ');
        } else {
            return dateObj.toLocaleDateString('fr-FR');
        }
    }
}

// Create global language manager instance
window.languageManager = new LanguageManager();
