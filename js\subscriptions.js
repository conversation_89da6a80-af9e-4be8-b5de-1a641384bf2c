/**
 * Subscriptions Management System
 * Handles subscription-related operations
 */

class SubscriptionsManager {
    constructor() {
        this.subscriptions = [];
        this.initializeSubscriptions();
    }

    /**
     * Initialize subscriptions management
     */
    initializeSubscriptions() {
        this.loadSubscriptions();
        this.bindEvents();
        this.renderSubscriptionsGrid();
    }

    /**
     * Load subscriptions from storage
     */
    loadSubscriptions() {
        this.subscriptions = window.storage.getSubscriptions();
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Add subscription button
        const addSubscriptionBtn = document.getElementById('add-subscription-btn');
        if (addSubscriptionBtn) {
            addSubscriptionBtn.addEventListener('click', () => this.showSubscriptionModal());
        }
    }

    /**
     * Render subscriptions grid
     */
    renderSubscriptionsGrid() {
        const grid = document.getElementById('subscriptions-grid');
        if (!grid) return;

        if (this.subscriptions.length === 0) {
            grid.innerHTML = `
                <div class="card">
                    <div class="card-body text-center">
                        <p data-key="no_subscriptions">${window.languageManager.getTranslation('no_subscriptions') || 'لا توجد اشتراكات'}</p>
                    </div>
                </div>
            `;
            return;
        }

        // Sort subscriptions by year
        const sortedSubscriptions = [...this.subscriptions].sort((a, b) => a.year - b.year);

        grid.innerHTML = sortedSubscriptions.map(subscription => `
            <div class="subscription-card">
                <div class="subscription-header">
                    <div class="subscription-year">${subscription.year}</div>
                    <div class="subscription-amount">${window.languageManager.formatCurrency(subscription.amount)}</div>
                </div>
                <div class="subscription-info">
                    <h4>${this.escapeHtml(subscription.name)}</h4>
                    <p><strong>تاريخ الاستحقاق:</strong> ${window.languageManager.formatDate(subscription.dueDate)}</p>
                </div>
                <div class="subscription-actions">
                    <button class="btn btn-outline btn-sm" onclick="subscriptionsManager.editSubscription('${subscription.id}')">
                        <i class="bi bi-pencil"></i>
                        <span data-key="edit">${window.languageManager.getTranslation('edit')}</span>
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="subscriptionsManager.deleteSubscription('${subscription.id}')">
                        <i class="bi bi-trash"></i>
                        <span data-key="delete">${window.languageManager.getTranslation('delete')}</span>
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * Show subscription modal for adding/editing
     */
    showSubscriptionModal(subscriptionId = null) {
        const subscription = subscriptionId ? this.subscriptions.find(s => s.id === subscriptionId) : null;
        const isEdit = !!subscription;

        const modalHtml = `
            <div class="modal-overlay" id="subscription-modal">
                <div class="modal">
                    <div class="modal-header">
                        <h3 data-key="subscription_details">${isEdit ? 'تعديل الاشتراك' : 'إضافة اشتراك جديد'}</h3>
                        <button type="button" class="btn-icon" onclick="subscriptionsManager.closeSubscriptionModal()">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="subscription-form">
                            <div class="form-group">
                                <label data-key="subscription_name">اسم الاشتراك</label>
                                <input type="text" id="subscriptionName" class="form-control" value="${subscription ? this.escapeHtml(subscription.name) : ''}" required>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label data-key="year">السنة</label>
                                    <input type="number" id="subscriptionYear" class="form-control" min="2018" max="2050" value="${subscription ? subscription.year : new Date().getFullYear()}" required>
                                </div>
                                <div class="form-group">
                                    <label data-key="amount">المبلغ (د.ج)</label>
                                    <input type="number" id="subscriptionAmount" class="form-control" min="0" step="100" value="${subscription ? subscription.amount : 5000}" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label data-key="due_date">تاريخ الاستحقاق</label>
                                <input type="date" id="subscriptionDueDate" class="form-control" value="${subscription ? subscription.dueDate : ''}" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="subscriptionsManager.closeSubscriptionModal()">
                            <span data-key="cancel">${window.languageManager.getTranslation('cancel')}</span>
                        </button>
                        <button type="button" class="btn btn-primary" onclick="subscriptionsManager.saveSubscription('${subscription ? subscription.id : ''}')">
                            <span data-key="save">${window.languageManager.getTranslation('save')}</span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        document.body.classList.add('modal-open');

        // Set default due date if not editing
        if (!subscription) {
            const year = new Date().getFullYear();
            document.getElementById('subscriptionDueDate').value = `${year}-12-31`;
        }
    }

    /**
     * Save subscription
     */
    saveSubscription(subscriptionId) {
        const form = document.getElementById('subscription-form');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const name = document.getElementById('subscriptionName').value.trim();
        const year = parseInt(document.getElementById('subscriptionYear').value);
        const amount = parseFloat(document.getElementById('subscriptionAmount').value);
        const dueDate = document.getElementById('subscriptionDueDate').value;

        // Check if year already exists (for new subscriptions)
        if (!subscriptionId) {
            const existingSubscription = this.subscriptions.find(s => s.year === year);
            if (existingSubscription) {
                alert('يوجد اشتراك لهذه السنة بالفعل');
                return;
            }
        }

        const subscriptionData = {
            name,
            year,
            amount,
            dueDate
        };

        if (subscriptionId) {
            subscriptionData.id = subscriptionId;
        }

        const savedSubscription = window.storage.saveSubscription(subscriptionData);
        
        if (savedSubscription) {
            this.showNotification('تم حفظ الاشتراك بنجاح', 'success');
            this.closeSubscriptionModal();
            this.loadSubscriptions();
            this.renderSubscriptionsGrid();
        }
    }

    /**
     * Edit subscription
     */
    editSubscription(subscriptionId) {
        this.showSubscriptionModal(subscriptionId);
    }

    /**
     * Delete subscription
     */
    deleteSubscription(subscriptionId) {
        const subscription = this.subscriptions.find(s => s.id === subscriptionId);
        if (!subscription) return;

        if (confirm(`هل أنت متأكد من حذف اشتراك ${subscription.name}؟`)) {
            if (window.storage.deleteSubscription(subscriptionId)) {
                this.showNotification('تم حذف الاشتراك بنجاح', 'success');
                this.loadSubscriptions();
                this.renderSubscriptionsGrid();
            }
        }
    }

    /**
     * Close subscription modal
     */
    closeSubscriptionModal() {
        const modal = document.getElementById('subscription-modal');
        if (modal) {
            modal.remove();
            document.body.classList.remove('modal-open');
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        // Simple notification implementation
        alert(message);
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    /**
     * Get subscription by year
     */
    getSubscriptionByYear(year) {
        return this.subscriptions.find(s => s.year === year);
    }

    /**
     * Get all subscription years
     */
    getSubscriptionYears() {
        return this.subscriptions.map(s => s.year).sort((a, b) => a - b);
    }

    /**
     * Calculate total subscription amount for a year
     */
    getTotalAmountForYear(year) {
        const subscription = this.getSubscriptionByYear(year);
        return subscription ? subscription.amount : 0;
    }

    /**
     * Get overdue subscriptions
     */
    getOverdueSubscriptions() {
        const currentDate = new Date();
        return this.subscriptions.filter(subscription => {
            const dueDate = new Date(subscription.dueDate);
            return dueDate < currentDate;
        });
    }

    /**
     * Get upcoming subscriptions (due in next 30 days)
     */
    getUpcomingSubscriptions() {
        const currentDate = new Date();
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(currentDate.getDate() + 30);

        return this.subscriptions.filter(subscription => {
            const dueDate = new Date(subscription.dueDate);
            return dueDate >= currentDate && dueDate <= thirtyDaysFromNow;
        });
    }
}

// Create global subscriptions manager instance
window.subscriptionsManager = new SubscriptionsManager();
