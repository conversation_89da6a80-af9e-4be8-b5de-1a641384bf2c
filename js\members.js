/**
 * Members Management System
 * Handles all member-related operations
 */

class MembersManager {
    constructor() {
        this.currentFilter = 'all';
        this.searchTerm = '';
        this.members = [];
        this.initializeMembers();
    }

    /**
     * Initialize members management
     */
    initializeMembers() {
        this.loadMembers();
        this.bindEvents();
        this.renderMembersTable();
    }

    /**
     * Load members from storage
     */
    loadMembers() {
        this.members = window.storage.getMembers();
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Add member button
        const addMemberBtn = document.getElementById('add-member-btn');
        if (addMemberBtn) {
            addMemberBtn.addEventListener('click', () => this.showMemberModal());
        }

        // Search input
        const searchInput = document.getElementById('member-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value.toLowerCase();
                this.renderMembersTable();
            });
        }

        // Filter buttons
        document.querySelectorAll('[data-filter]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.currentFilter = e.target.dataset.filter;
                this.updateFilterButtons();
                this.renderMembersTable();
            });
        });
    }

    /**
     * Update filter button states
     */
    updateFilterButtons() {
        document.querySelectorAll('[data-filter]').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.filter === this.currentFilter) {
                btn.classList.add('active');
            }
        });
    }

    /**
     * Filter members based on current filter and search term
     */
    getFilteredMembers() {
        let filtered = [...this.members];

        // Apply search filter
        if (this.searchTerm) {
            filtered = filtered.filter(member => 
                member.fullName.toLowerCase().includes(this.searchTerm) ||
                member.groupNumber.toString().includes(this.searchTerm) ||
                member.buildingApt.toLowerCase().includes(this.searchTerm) ||
                (member.phone && member.phone.includes(this.searchTerm))
            );
        }

        // Apply status filter
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(member => {
                const status = this.getMemberStatus(member);
                return status === this.currentFilter;
            });
        }

        return filtered;
    }

    /**
     * Get member status based on subscription payments
     */
    getMemberStatus(member) {
        // This is a simplified status calculation
        // In a real application, you would check payment history
        const currentYear = new Date().getFullYear();
        const hasCurrentYearPayment = member.subscriptionYears && 
            member.subscriptionYears.includes(currentYear);
        
        return hasCurrentYearPayment ? 'active' : 'overdue';
    }

    /**
     * Render members table
     */
    renderMembersTable() {
        const tbody = document.getElementById('members-tbody');
        if (!tbody) return;

        const filteredMembers = this.getFilteredMembers();

        if (filteredMembers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">
                        <span data-key="no_members">${window.languageManager.getTranslation('no_members')}</span>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = filteredMembers.map(member => `
            <tr>
                <td>${this.escapeHtml(member.fullName)}</td>
                <td>${this.escapeHtml(member.groupNumber)}</td>
                <td>${this.escapeHtml(member.buildingApt)}</td>
                <td>${this.escapeHtml(member.phone || '-')}</td>
                <td>
                    <span class="status-badge status-${this.getMemberStatus(member)}">
                        ${window.languageManager.getTranslation(this.getMemberStatus(member))}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon btn-sm" onclick="membersManager.viewMember('${member.id}')" title="${window.languageManager.getTranslation('view')}">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn-icon btn-sm" onclick="membersManager.editMember('${member.id}')" title="${window.languageManager.getTranslation('edit')}">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn-icon btn-sm btn-danger" onclick="membersManager.deleteMember('${member.id}')" title="${window.languageManager.getTranslation('delete')}">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * Show member modal for adding/editing
     */
    showMemberModal(memberId = null) {
        const member = memberId ? window.storage.getMemberById(memberId) : null;
        const isEdit = !!member;

        const modalHtml = `
            <div class="modal-overlay" id="member-modal">
                <div class="modal">
                    <div class="modal-header">
                        <h3 data-key="member_details">${window.languageManager.getTranslation('member_details')}</h3>
                        <button class="btn-icon" onclick="membersManager.closeMemberModal()">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="member-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label data-key="first_name">${window.languageManager.getTranslation('first_name')}</label>
                                    <input type="text" id="firstName" class="form-control" value="${member ? this.escapeHtml(member.firstName || '') : ''}" required>
                                </div>
                                <div class="form-group">
                                    <label data-key="last_name">${window.languageManager.getTranslation('last_name')}</label>
                                    <input type="text" id="lastName" class="form-control" value="${member ? this.escapeHtml(member.lastName || '') : ''}" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label data-key="group_number">${window.languageManager.getTranslation('group_number')}</label>
                                    <input type="number" id="groupNumber" class="form-control" value="${member ? member.groupNumber || '' : ''}" required>
                                </div>
                                <div class="form-group">
                                    <label data-key="building_apt">${window.languageManager.getTranslation('building_apt')}</label>
                                    <input type="text" id="buildingApt" class="form-control" value="${member ? this.escapeHtml(member.buildingApt || '') : ''}" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label data-key="phone">${window.languageManager.getTranslation('phone')}</label>
                                    <input type="tel" id="phone" class="form-control" value="${member ? this.escapeHtml(member.phone || '') : ''}">
                                </div>
                                <div class="form-group">
                                    <label data-key="email">${window.languageManager.getTranslation('email')}</label>
                                    <input type="email" id="email" class="form-control" value="${member ? this.escapeHtml(member.email || '') : ''}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label data-key="address">${window.languageManager.getTranslation('address')}</label>
                                <textarea id="address" class="form-control" rows="3">${member ? this.escapeHtml(member.address || '') : ''}</textarea>
                            </div>
                            <div class="form-group">
                                <label data-key="land_title">${window.languageManager.getTranslation('land_title')}</label>
                                <input type="text" id="landTitle" class="form-control" value="${member ? this.escapeHtml(member.landTitle || '') : ''}">
                            </div>
                            <div class="form-group">
                                <label data-key="subscription_years">${window.languageManager.getTranslation('subscription_years')}</label>
                                <div class="subscription-years">
                                    ${this.generateSubscriptionYearsCheckboxes(member)}
                                </div>
                            </div>
                            <div class="form-group">
                                <label data-key="notes">${window.languageManager.getTranslation('notes')}</label>
                                <textarea id="notes" class="form-control" rows="3">${member ? this.escapeHtml(member.notes || '') : ''}</textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="membersManager.closeMemberModal()">
                            <span data-key="cancel">${window.languageManager.getTranslation('cancel')}</span>
                        </button>
                        <button type="button" class="btn btn-primary" onclick="membersManager.saveMember('${member ? member.id : ''}')">
                            <span data-key="save">${window.languageManager.getTranslation('save')}</span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        document.body.classList.add('modal-open');
    }

    /**
     * Generate subscription years checkboxes
     */
    generateSubscriptionYearsCheckboxes(member) {
        const subscriptions = window.storage.getSubscriptions();
        const memberYears = member ? (member.subscriptionYears || []) : [];

        return subscriptions.map(sub => `
            <label class="checkbox-label">
                <input type="checkbox" value="${sub.year}" ${memberYears.includes(sub.year) ? 'checked' : ''}>
                <span class="checkbox-custom"></span>
                ${sub.name} (${window.languageManager.formatCurrency(sub.amount)})
            </label>
        `).join('');
    }

    /**
     * Save member
     */
    saveMember(memberId) {
        const form = document.getElementById('member-form');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const firstName = document.getElementById('firstName').value.trim();
        const lastName = document.getElementById('lastName').value.trim();
        const groupNumber = parseInt(document.getElementById('groupNumber').value);
        const buildingApt = document.getElementById('buildingApt').value.trim();
        const phone = document.getElementById('phone').value.trim();
        const email = document.getElementById('email').value.trim();
        const address = document.getElementById('address').value.trim();
        const landTitle = document.getElementById('landTitle').value.trim();
        const notes = document.getElementById('notes').value.trim();

        // Get selected subscription years
        const subscriptionYears = Array.from(document.querySelectorAll('.subscription-years input[type="checkbox"]:checked'))
            .map(cb => parseInt(cb.value));

        const memberData = {
            firstName,
            lastName,
            fullName: `${firstName} ${lastName}`,
            groupNumber,
            buildingApt,
            phone,
            email,
            address,
            landTitle,
            subscriptionYears,
            notes
        };

        if (memberId) {
            memberData.id = memberId;
        }

        const savedMember = window.storage.saveMember(memberData);
        
        if (savedMember) {
            this.showNotification(window.languageManager.getTranslation('member_saved_successfully'), 'success');
            this.closeMemberModal();
            this.loadMembers();
            this.renderMembersTable();
            this.updateDashboardStats();
        }
    }

    /**
     * View member details
     */
    viewMember(memberId) {
        const member = window.storage.getMemberById(memberId);
        if (!member) return;

        // For now, just show edit modal in read-only mode
        // In a full implementation, you'd create a separate view modal
        this.editMember(memberId);
    }

    /**
     * Edit member
     */
    editMember(memberId) {
        this.showMemberModal(memberId);
    }

    /**
     * Delete member
     */
    deleteMember(memberId) {
        const member = window.storage.getMemberById(memberId);
        if (!member) return;

        if (confirm(window.languageManager.getTranslation('confirm_delete_member'))) {
            if (window.storage.deleteMember(memberId)) {
                this.showNotification(window.languageManager.getTranslation('member_deleted_successfully'), 'success');
                this.loadMembers();
                this.renderMembersTable();
                this.updateDashboardStats();
            }
        }
    }

    /**
     * Close member modal
     */
    closeMemberModal() {
        const modal = document.getElementById('member-modal');
        if (modal) {
            modal.remove();
            document.body.classList.remove('modal-open');
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        // Simple notification implementation
        // In a full implementation, you'd create a proper notification system
        alert(message);
    }

    /**
     * Update dashboard statistics
     */
    updateDashboardStats() {
        const totalMembersEl = document.getElementById('total-members');
        const overdueCountEl = document.getElementById('overdue-count');

        if (totalMembersEl) {
            totalMembersEl.textContent = this.members.length;
        }

        if (overdueCountEl) {
            const overdueCount = this.members.filter(member => this.getMemberStatus(member) === 'overdue').length;
            overdueCountEl.textContent = overdueCount;
        }
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }
}

// Create global members manager instance
window.membersManager = new MembersManager();
