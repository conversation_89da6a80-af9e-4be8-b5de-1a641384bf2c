/**
 * Main Application Controller
 * Handles overall app initialization and coordination
 */

class SyndicApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.sidebarOpen = true;
        this.theme = 'light';
        this.initialize();
    }

    /**
     * Initialize the application
     */
    initialize() {
        this.bindEvents();
        this.initializeTheme();
        this.showSection('dashboard');
        this.updateDashboard();
        this.checkResponsive();
        
        // Initialize other managers
        this.initializeManagers();
        
        console.log('Syndic Management System initialized successfully');
    }

    /**
     * Initialize other managers
     */
    initializeManagers() {
        // Language manager is already initialized globally
        // Members manager is already initialized globally
        
        // Initialize other managers as needed
        this.initializeNavigation();
        this.initializeModals();
    }

    /**
     * Bind global event listeners
     */
    bindEvents() {
        // Language toggle buttons
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const lang = e.target.dataset.lang;
                window.languageManager.switchLanguage(lang);
            });
        });

        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }

        // Navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.closest('.nav-link').dataset.section;
                this.showSection(section);
            });
        });

        // Window resize handler
        window.addEventListener('resize', () => this.checkResponsive());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    /**
     * Initialize navigation
     */
    initializeNavigation() {
        // Set active navigation item
        this.updateActiveNavigation();
    }

    /**
     * Initialize modals
     */
    initializeModals() {
        // Close modals when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeAllModals();
            }
        });

        // Close modals with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }

    /**
     * Show specific section
     */
    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionName;
            this.updateActiveNavigation();
            
            // Load section-specific data
            this.loadSectionData(sectionName);
        }
    }

    /**
     * Update active navigation item
     */
    updateActiveNavigation() {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
            if (link.dataset.section === this.currentSection) {
                link.classList.add('active');
            }
        });
    }

    /**
     * Load section-specific data
     */
    loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.updateDashboard();
                break;
            case 'members':
                if (window.membersManager) {
                    window.membersManager.loadMembers();
                    window.membersManager.renderMembersTable();
                }
                break;
            case 'subscriptions':
                this.loadSubscriptions();
                break;
            case 'receipts':
                this.loadReceipts();
                break;
            case 'reports':
                this.loadReports();
                break;
            case 'court-docs':
                this.loadCourtDocs();
                break;
            case 'settings':
                this.loadSettings();
                break;
        }
    }

    /**
     * Update dashboard statistics and activities
     */
    updateDashboard() {
        const members = window.storage.getMembers();
        const receipts = window.storage.getReceipts();
        const activities = window.storage.getActivities();

        // Update statistics
        document.getElementById('total-members').textContent = members.length;
        
        // Calculate total dues
        const totalDues = receipts.reduce((sum, receipt) => sum + (receipt.amount || 0), 0);
        document.getElementById('total-dues').textContent = window.languageManager.formatCurrency(totalDues);
        
        // Calculate overdue members
        const overdueCount = members.filter(member => {
            const currentYear = new Date().getFullYear();
            return !member.subscriptionYears || !member.subscriptionYears.includes(currentYear);
        }).length;
        document.getElementById('overdue-count').textContent = overdueCount;
        
        // Update receipts count
        document.getElementById('receipts-count').textContent = receipts.length;

        // Update recent activities
        this.updateRecentActivities(activities.slice(0, 5));
    }

    /**
     * Update recent activities display
     */
    updateRecentActivities(activities) {
        const activitiesContainer = document.getElementById('recent-activities');
        if (!activitiesContainer) return;

        if (activities.length === 0) {
            activitiesContainer.innerHTML = `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="bi bi-info-circle"></i>
                    </div>
                    <div class="activity-content">
                        <p data-key="no_activities">${window.languageManager.getTranslation('no_activities')}</p>
                    </div>
                </div>
            `;
            return;
        }

        activitiesContainer.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="bi bi-${this.getActivityIcon(activity.type)}"></i>
                </div>
                <div class="activity-content">
                    <p>${this.formatActivityMessage(activity)}</p>
                    <small class="text-muted">${window.languageManager.formatDate(activity.timestamp)}</small>
                </div>
            </div>
        `).join('');
    }

    /**
     * Get icon for activity type
     */
    getActivityIcon(type) {
        const icons = {
            member: 'person',
            subscription: 'calendar-check',
            receipt: 'receipt',
            court_doc: 'file-earmark-text',
            system: 'gear'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * Format activity message
     */
    formatActivityMessage(activity) {
        const actionMap = {
            created: window.languageManager.getTranslation('created') || 'created',
            updated: window.languageManager.getTranslation('updated') || 'updated',
            deleted: window.languageManager.getTranslation('deleted') || 'deleted'
        };
        
        return `${actionMap[activity.action] || activity.action} ${activity.target}`;
    }

    /**
     * Toggle theme between light and dark
     */
    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        document.body.classList.toggle('dark-theme', this.theme === 'dark');
        
        const themeIcon = document.querySelector('#theme-toggle i');
        if (themeIcon) {
            themeIcon.className = this.theme === 'dark' ? 'bi bi-sun' : 'bi bi-moon';
        }
        
        localStorage.setItem('syndic_theme', this.theme);
    }

    /**
     * Initialize theme from storage
     */
    initializeTheme() {
        const savedTheme = localStorage.getItem('syndic_theme');
        if (savedTheme) {
            this.theme = savedTheme;
            document.body.classList.toggle('dark-theme', this.theme === 'dark');
            
            const themeIcon = document.querySelector('#theme-toggle i');
            if (themeIcon) {
                themeIcon.className = this.theme === 'dark' ? 'bi bi-sun' : 'bi bi-moon';
            }
        }
    }

    /**
     * Toggle sidebar visibility
     */
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        
        this.sidebarOpen = !this.sidebarOpen;
        
        if (sidebar) {
            sidebar.classList.toggle('open', this.sidebarOpen);
        }
        
        if (mainContent) {
            mainContent.classList.toggle('sidebar-closed', !this.sidebarOpen);
        }
    }

    /**
     * Check responsive breakpoints and adjust UI
     */
    checkResponsive() {
        const isMobile = window.innerWidth <= 768;
        const sidebar = document.getElementById('sidebar');
        
        if (isMobile) {
            this.sidebarOpen = false;
            if (sidebar) {
                sidebar.classList.remove('open');
            }
        } else {
            this.sidebarOpen = true;
            if (sidebar) {
                sidebar.classList.add('open');
            }
        }
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case '1':
                    e.preventDefault();
                    this.showSection('dashboard');
                    break;
                case '2':
                    e.preventDefault();
                    this.showSection('members');
                    break;
                case '3':
                    e.preventDefault();
                    this.showSection('subscriptions');
                    break;
                case 'n':
                    e.preventDefault();
                    if (this.currentSection === 'members' && window.membersManager) {
                        window.membersManager.showMemberModal();
                    }
                    break;
            }
        }
    }

    /**
     * Close all open modals
     */
    closeAllModals() {
        document.querySelectorAll('.modal-overlay').forEach(modal => {
            modal.remove();
        });
        document.body.classList.remove('modal-open');
    }

    /**
     * Show loading overlay
     */
    showLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }
    }

    /**
     * Hide loading overlay
     */
    hideLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    /**
     * Load section-specific data methods
     */
    loadSubscriptions() {
        if (window.subscriptionsManager) {
            window.subscriptionsManager.loadSubscriptions();
            window.subscriptionsManager.renderSubscriptionsGrid();
        }
    }

    loadReceipts() {
        if (window.receiptsManager) {
            window.receiptsManager.loadReceipts();
            window.receiptsManager.renderReceiptsTable();
        }
    }

    loadReports() {
        // Reports are generated on demand, no loading needed
        console.log('Reports section loaded');
    }

    loadCourtDocs() {
        // Court documents are created on demand, no loading needed
        console.log('Court documents section loaded');
    }

    loadSettings() {
        this.loadOrganizationSettings();
    }

    /**
     * Load organization settings into form
     */
    loadOrganizationSettings() {
        const settings = window.storage.getSettings();

        const orgNameInput = document.getElementById('org-name');
        const orgAddressInput = document.getElementById('org-address');
        const orgPhoneInput = document.getElementById('org-phone');
        const orgEmailInput = document.getElementById('org-email');

        if (orgNameInput) orgNameInput.value = settings.organizationName || '';
        if (orgAddressInput) orgAddressInput.value = settings.organizationAddress || '';
        if (orgPhoneInput) orgPhoneInput.value = settings.organizationPhone || '';
        if (orgEmailInput) orgEmailInput.value = settings.organizationEmail || '';
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.syndicApp = new SyndicApp();
});
