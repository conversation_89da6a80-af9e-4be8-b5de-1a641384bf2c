/**
 * Receipts Management System
 * Handles receipt creation and management
 */

class ReceiptsManager {
    constructor() {
        this.receipts = [];
        this.initializeReceipts();
    }

    /**
     * Initialize receipts management
     */
    initializeReceipts() {
        this.loadReceipts();
        this.bindEvents();
        this.renderReceiptsTable();
    }

    /**
     * Load receipts from storage
     */
    loadReceipts() {
        this.receipts = window.storage.getReceipts();
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Create receipt button
        const createReceiptBtn = document.getElementById('create-receipt-btn');
        if (createReceiptBtn) {
            createReceiptBtn.addEventListener('click', () => this.showReceiptModal());
        }
    }

    /**
     * Render receipts table
     */
    renderReceiptsTable() {
        const tbody = document.getElementById('receipts-tbody');
        if (!tbody) return;

        if (this.receipts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">
                        <span data-key="no_receipts">لا توجد إيصالات</span>
                    </td>
                </tr>
            `;
            return;
        }

        // Sort receipts by date (newest first)
        const sortedReceipts = [...this.receipts].sort((a, b) => new Date(b.receiptDate) - new Date(a.receiptDate));

        tbody.innerHTML = sortedReceipts.map(receipt => {
            const member = window.storage.getMemberById(receipt.memberId);
            const memberName = member ? member.fullName : 'عضو محذوف';
            
            return `
                <tr>
                    <td>${this.escapeHtml(receipt.receiptNumber)}</td>
                    <td>${this.escapeHtml(memberName)}</td>
                    <td>${window.languageManager.formatCurrency(receipt.amount)}</td>
                    <td>${window.languageManager.formatDate(receipt.receiptDate)}</td>
                    <td>
                        <span class="payment-method-badge payment-${receipt.paymentMethod}">
                            ${this.getPaymentMethodText(receipt.paymentMethod)}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-icon btn-sm" onclick="receiptsManager.viewReceipt('${receipt.id}')" title="عرض">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn-icon btn-sm" onclick="receiptsManager.printReceipt('${receipt.id}')" title="طباعة">
                                <i class="bi bi-printer"></i>
                            </button>
                            <button class="btn-icon btn-sm btn-danger" onclick="receiptsManager.deleteReceipt('${receipt.id}')" title="حذف">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    /**
     * Show receipt modal for creating
     */
    showReceiptModal() {
        const members = window.storage.getMembers();
        const subscriptions = window.storage.getSubscriptions();

        if (members.length === 0) {
            alert('يجب إضافة أعضاء أولاً قبل إنشاء الإيصالات');
            return;
        }

        const modalHtml = `
            <div class="modal-overlay" id="receipt-modal">
                <div class="modal">
                    <div class="modal-header">
                        <h3 data-key="create_receipt">إنشاء إيصال جديد</h3>
                        <button type="button" class="btn-icon" onclick="receiptsManager.closeReceiptModal()">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="receipt-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label data-key="full_name">العضو</label>
                                    <select id="receiptMember" class="form-control" required onchange="receiptsManager.onMemberChange()">
                                        <option value="">اختر العضو</option>
                                        ${members.map(member => `
                                            <option value="${member.id}">${this.escapeHtml(member.fullName)} - ${member.groupNumber}</option>
                                        `).join('')}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label data-key="receipt_date">تاريخ الإيصال</label>
                                    <input type="date" id="receiptDate" class="form-control" value="${new Date().toISOString().split('T')[0]}" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label data-key="subscription_years">الاشتراكات</label>
                                <div class="subscription-selection" id="subscription-selection">
                                    <p class="text-muted">اختر عضواً أولاً لعرض الاشتراكات المتاحة</p>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label data-key="amount">المبلغ الإجمالي (د.ج)</label>
                                    <input type="number" id="receiptAmount" class="form-control" min="0" step="100" readonly>
                                </div>
                                <div class="form-group">
                                    <label data-key="payment_method">طريقة الدفع</label>
                                    <select id="receiptPaymentMethod" class="form-control" required>
                                        <option value="cash">نقداً</option>
                                        <option value="check">شيك</option>
                                        <option value="bank_transfer">تحويل بنكي</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label data-key="notes">ملاحظات</label>
                                <textarea id="receiptNotes" class="form-control" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="receiptsManager.closeReceiptModal()">
                            <span data-key="cancel">إلغاء</span>
                        </button>
                        <button type="button" class="btn btn-primary" onclick="receiptsManager.saveReceipt()">
                            <span data-key="save">حفظ وطباعة</span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        document.body.classList.add('modal-open');
    }

    /**
     * Handle member selection change
     */
    onMemberChange() {
        const memberSelect = document.getElementById('receiptMember');
        const subscriptionSelection = document.getElementById('subscription-selection');
        const amountInput = document.getElementById('receiptAmount');

        if (!memberSelect.value) {
            subscriptionSelection.innerHTML = '<p class="text-muted">اختر عضواً أولاً لعرض الاشتراكات المتاحة</p>';
            amountInput.value = '';
            return;
        }

        const member = window.storage.getMemberById(memberSelect.value);
        const subscriptions = window.storage.getSubscriptions();
        const memberSubscriptions = member.subscriptionYears || [];

        // Show available subscriptions for this member
        const availableSubscriptions = subscriptions.filter(sub => 
            memberSubscriptions.includes(sub.year)
        );

        if (availableSubscriptions.length === 0) {
            subscriptionSelection.innerHTML = '<p class="text-muted">لا توجد اشتراكات مسجلة لهذا العضو</p>';
            amountInput.value = '';
            return;
        }

        subscriptionSelection.innerHTML = `
            <div class="subscription-checkboxes">
                ${availableSubscriptions.map(sub => `
                    <label class="checkbox-label">
                        <input type="checkbox" value="${sub.id}" data-amount="${sub.amount}" onchange="receiptsManager.calculateTotal()">
                        <span class="checkbox-custom"></span>
                        ${sub.name} - ${window.languageManager.formatCurrency(sub.amount)}
                    </label>
                `).join('')}
            </div>
        `;
    }

    /**
     * Calculate total amount based on selected subscriptions
     */
    calculateTotal() {
        const checkboxes = document.querySelectorAll('#subscription-selection input[type="checkbox"]:checked');
        const total = Array.from(checkboxes).reduce((sum, cb) => sum + parseFloat(cb.dataset.amount), 0);
        document.getElementById('receiptAmount').value = total;
    }

    /**
     * Save receipt
     */
    saveReceipt() {
        const form = document.getElementById('receipt-form');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const memberId = document.getElementById('receiptMember').value;
        const receiptDate = document.getElementById('receiptDate').value;
        const amount = parseFloat(document.getElementById('receiptAmount').value);
        const paymentMethod = document.getElementById('receiptPaymentMethod').value;
        const notes = document.getElementById('receiptNotes').value.trim();

        // Get selected subscriptions
        const selectedSubscriptions = Array.from(document.querySelectorAll('#subscription-selection input[type="checkbox"]:checked'))
            .map(cb => cb.value);

        if (selectedSubscriptions.length === 0) {
            alert('يجب اختيار اشتراك واحد على الأقل');
            return;
        }

        if (amount <= 0) {
            alert('المبلغ يجب أن يكون أكبر من صفر');
            return;
        }

        const receiptData = {
            memberId,
            receiptDate,
            amount,
            paymentMethod,
            subscriptionIds: selectedSubscriptions,
            notes
        };

        const savedReceipt = window.storage.saveReceipt(receiptData);
        
        if (savedReceipt) {
            this.showNotification('تم إنشاء الإيصال بنجاح', 'success');
            this.closeReceiptModal();
            this.loadReceipts();
            this.renderReceiptsTable();
            
            // Optionally print the receipt
            this.printReceipt(savedReceipt.id);
        }
    }

    /**
     * View receipt details
     */
    viewReceipt(receiptId) {
        const receipt = this.receipts.find(r => r.id === receiptId);
        if (!receipt) return;

        const member = window.storage.getMemberById(receipt.memberId);
        
        // For now, just show an alert with receipt details
        // In a full implementation, you'd create a detailed view modal
        alert(`
إيصال رقم: ${receipt.receiptNumber}
العضو: ${member ? member.fullName : 'غير معروف'}
المبلغ: ${window.languageManager.formatCurrency(receipt.amount)}
التاريخ: ${window.languageManager.formatDate(receipt.receiptDate)}
طريقة الدفع: ${this.getPaymentMethodText(receipt.paymentMethod)}
        `);
    }

    /**
     * Print receipt
     */
    printReceipt(receiptId) {
        const receipt = this.receipts.find(r => r.id === receiptId);
        if (!receipt) return;

        const member = window.storage.getMemberById(receipt.memberId);
        const settings = window.storage.getSettings();

        // Create print content
        const printContent = `
            <div style="font-family: Arial, sans-serif; direction: rtl; text-align: right; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1>${settings.organizationName || 'نقابة الملاك'}</h1>
                    <p>${settings.organizationAddress || ''}</p>
                    <p>هاتف: ${settings.organizationPhone || ''}</p>
                </div>
                
                <div style="border: 2px solid #000; padding: 20px; margin: 20px 0;">
                    <h2 style="text-align: center;">إيصال دفع</h2>
                    <p><strong>رقم الإيصال:</strong> ${receipt.receiptNumber}</p>
                    <p><strong>التاريخ:</strong> ${window.languageManager.formatDate(receipt.receiptDate)}</p>
                    <p><strong>اسم العضو:</strong> ${member ? member.fullName : 'غير معروف'}</p>
                    <p><strong>رقم المجموعة:</strong> ${member ? member.groupNumber : ''}</p>
                    <p><strong>البناء/الشقة:</strong> ${member ? member.buildingApt : ''}</p>
                    <p><strong>المبلغ المدفوع:</strong> ${window.languageManager.formatCurrency(receipt.amount)}</p>
                    <p><strong>طريقة الدفع:</strong> ${this.getPaymentMethodText(receipt.paymentMethod)}</p>
                    ${receipt.notes ? `<p><strong>ملاحظات:</strong> ${receipt.notes}</p>` : ''}
                </div>
                
                <div style="margin-top: 50px;">
                    <p>التوقيع: ________________</p>
                    <p>الختم: ________________</p>
                </div>
            </div>
        `;

        // Open print window
        const printWindow = window.open('', '_blank');
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.print();
    }

    /**
     * Delete receipt
     */
    deleteReceipt(receiptId) {
        const receipt = this.receipts.find(r => r.id === receiptId);
        if (!receipt) return;

        if (confirm(`هل أنت متأكد من حذف الإيصال رقم ${receipt.receiptNumber}؟`)) {
            // Note: In a real system, you might want to mark as deleted rather than actually delete
            const receiptIndex = this.receipts.findIndex(r => r.id === receiptId);
            if (receiptIndex !== -1) {
                this.receipts.splice(receiptIndex, 1);
                window.storage.setItem(window.storage.storageKeys.RECEIPTS, this.receipts);
                this.showNotification('تم حذف الإيصال بنجاح', 'success');
                this.renderReceiptsTable();
            }
        }
    }

    /**
     * Close receipt modal
     */
    closeReceiptModal() {
        const modal = document.getElementById('receipt-modal');
        if (modal) {
            modal.remove();
            document.body.classList.remove('modal-open');
        }
    }

    /**
     * Get payment method text
     */
    getPaymentMethodText(method) {
        const methods = {
            cash: 'نقداً',
            check: 'شيك',
            bank_transfer: 'تحويل بنكي'
        };
        return methods[method] || method;
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        alert(message);
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }
}

// Create global receipts manager instance
window.receiptsManager = new ReceiptsManager();
